<?php
/**
 * Design Loader Class
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SI_Design_Loader class for managing invoice designs
 *
 * @since 1.0.0
 */
class SI_Design_Loader {

    /**
     * Designs directory path
     *
     * @var string
     * @since 1.0.0
     */
    private $designs_path;

    /**
     * Designs directory URL
     *
     * @var string
     * @since 1.0.0
     */
    private $designs_url;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->designs_path = WIMP_PLUGIN_PATH . 'designs/';
        $this->designs_url = WIMP_PLUGIN_URL . 'designs/';

        // Initialize hooks
        $this->si_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function si_init_hooks() {
        add_action('wp_ajax_si_get_design_preview', array($this, 'si_ajax_get_design_preview'));
        add_action('wp_ajax_si_preview_design', array($this, 'si_ajax_preview_design'));
    }

    /**
     * Get all available designs
     *
     * @return array Available designs
     * @since 1.0.0
     */
    public function si_get_available_designs() {
        $designs = array();
        
        if (!is_dir($this->designs_path)) {
            return $designs;
        }

        $design_dirs = scandir($this->designs_path);
        
        foreach ($design_dirs as $dir) {
            if ($dir === '.' || $dir === '..' || !is_dir($this->designs_path . $dir)) {
                continue;
            }

            $template_file = $this->designs_path . $dir . '/template.php';
            $preview_file = $this->designs_path . $dir . '/preview.jpg';
            
            if (file_exists($template_file)) {
                $designs[$dir] = array(
                    'id' => $dir,
                    'name' => $this->si_format_design_name($dir),
                    'template_path' => $template_file,
                    'preview_url' => file_exists($preview_file) ? $this->designs_url . $dir . '/preview.jpg' : '',
                    'description' => $this->si_get_design_description($dir)
                );
            }
        }
        
        return $designs;
    }

    /**
     * Get design by ID
     *
     * @param string $design_id Design ID
     * @return array|null Design data or null if not found
     * @since 1.0.0
     */
    public function si_get_design($design_id) {
        $designs = $this->si_get_available_designs();
        return isset($designs[$design_id]) ? $designs[$design_id] : null;
    }

    /**
     * Load design template
     *
     * @param string $design_id Design ID
     * @param array $data Invoice data
     * @return string Rendered HTML
     * @since 1.0.0
     */
    public function si_load_design_template($design_id, $data = array()) {
        $design = $this->si_get_design($design_id);
        
        if (!$design || !file_exists($design['template_path'])) {
            return $this->si_get_fallback_template($data);
        }

        // Start output buffering
        ob_start();
        
        // Extract data for use in template
        extract($data);
        
        // Include the design template
        include $design['template_path'];
        
        // Get the rendered content
        $content = ob_get_clean();
        
        return $content;
    }

    /**
     * Get design CSS
     *
     * @param string $design_id Design ID
     * @return string CSS content
     * @since 1.0.0
     */
    public function si_get_design_css($design_id) {
        $css_file = $this->designs_path . $design_id . '/style.css';
        
        if (file_exists($css_file)) {
            return file_get_contents($css_file);
        }
        
        return $this->si_get_default_css();
    }

    /**
     * Format design name for display
     *
     * @param string $design_id Design ID
     * @return string Formatted name
     * @since 1.0.0
     */
    private function si_format_design_name($design_id) {
        return ucwords(str_replace(array('-', '_'), ' ', $design_id));
    }

    /**
     * Get design description
     *
     * @param string $design_id Design ID
     * @return string Design description
     * @since 1.0.0
     */
    private function si_get_design_description($design_id) {
        $descriptions = array(
            'classic' => __('A traditional and professional invoice design with clean lines and standard formatting.', 'simple-invoice'),
            'modern' => __('A contemporary design with modern typography and sleek visual elements.', 'simple-invoice'),
            'minimal' => __('A clean and minimalist design focusing on simplicity and readability.', 'simple-invoice')
        );
        
        return isset($descriptions[$design_id]) ? $descriptions[$design_id] : __('Custom invoice design template.', 'simple-invoice');
    }

    /**
     * Get fallback template when design is not found
     *
     * @param array $data Invoice data
     * @return string Fallback HTML template
     * @since 1.0.0
     */
    private function si_get_fallback_template($data) {
        $settings = si_get_settings();
        
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php echo esc_html__('Invoice', 'simple-invoice'); ?></title>
            <style>
                <?php echo $this->si_get_default_css(); ?>
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <div class="invoice-header">
                    <h1><?php echo esc_html($settings['business_name'] ?? get_bloginfo('name')); ?></h1>
                    <?php if (!empty($settings['business_address'])): ?>
                        <p><?php echo nl2br(esc_html($settings['business_address'])); ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="invoice-details">
                    <h2><?php echo esc_html__('Invoice', 'simple-invoice'); ?></h2>
                    <p><strong><?php echo esc_html__('Invoice Number:', 'simple-invoice'); ?></strong> <?php echo esc_html($data['invoice_number'] ?? ''); ?></p>
                    <p><strong><?php echo esc_html__('Date:', 'simple-invoice'); ?></strong> <?php echo esc_html(date('Y-m-d')); ?></p>
                </div>
                
                <div class="client-details">
                    <h3><?php echo esc_html__('Bill To:', 'simple-invoice'); ?></h3>
                    <?php if (isset($data['client'])): ?>
                        <p><strong><?php echo esc_html($data['client']['name']); ?></strong></p>
                        <?php if (!empty($data['client']['address'])): ?>
                            <p><?php echo nl2br(esc_html($data['client']['address'])); ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <?php
                // Get dynamic fields from template if available
                $table_fields = array();
                if (isset($data['template']) && !empty($data['template']->body_fields)) {
                    $table_fields = $data['template']->body_fields;
                }

                // Only show items table if fields are configured
                if (!empty($table_fields)): ?>
                    <div class="invoice-items">
                        <table>
                            <thead>
                                <tr>
                                    <?php foreach ($table_fields as $field): ?>
                                        <th><?php echo esc_html($field['label']); ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($data['items']) && is_array($data['items'])): ?>
                                    <?php foreach ($data['items'] as $item_index => $item): ?>
                                        <tr>
                                            <?php foreach ($table_fields as $field): ?>
                                                <td>
                                                    <?php
                                                    switch ($field['type']) {
                                                        case 'serial':
                                                            echo esc_html($item_index + 1);
                                                            break;
                                                        case 'calculated':
                                                            if (isset($field['formula']) && $field['formula'] === 'quantity * rate') {
                                                                $quantity = $item['quantity'] ?? 0;
                                                                $rate = $item['rate'] ?? 0;
                                                                echo esc_html($quantity * $rate);
                                                            } else {
                                                                echo esc_html($item[$field['name']] ?? '');
                                                            }
                                                            break;
                                                        default:
                                                            echo esc_html($item[$field['name']] ?? '');
                                                            break;
                                                    }
                                                    ?>
                                                </td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <?php
                // Get dynamic summary fields from template if available
                $summary_fields = array();
                if (isset($data['template']) && !empty($data['template']->summary_fields)) {
                    $summary_fields = $data['template']->summary_fields;
                }

                // Only show summary if fields are configured
                if (!empty($summary_fields)): ?>
                    <div class="invoice-summary">
                        <?php foreach ($summary_fields as $field): ?>
                            <?php
                            $field_value = 0;
                            switch ($field['name']) {
                                case 'subtotal':
                                    $field_value = $data['subtotal'] ?? 0;
                                    break;
                                case 'total':
                                case 'grand_total':
                                    $field_value = $data['total_amount'] ?? 0;
                                    break;
                                default:
                                    $field_value = $data[$field['name']] ?? 0;
                                    break;
                            }
                            ?>
                            <p><strong><?php echo esc_html($field['label']); ?>:</strong> <?php echo esc_html($field_value); ?></p>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Get default CSS styles
     *
     * @return string Default CSS
     * @since 1.0.0
     */
    private function si_get_default_css() {
        return '
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #000000;
            }
            .invoice-container {
                max-width: 800px;
                margin: 0 auto;
                background: #ffffff;
                padding: 30px;
                border: 1px solid #e7e7e7;
            }
            .invoice-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #f47a45;
                padding-bottom: 20px;
            }
            .invoice-header h1 {
                margin: 0;
                font-size: 28px;
                color: #f47a45;
            }
            .invoice-details, .client-details {
                margin-bottom: 30px;
            }
            .invoice-items table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
            }
            .invoice-items th,
            .invoice-items td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #e7e7e7;
            }
            .invoice-items th {
                background-color: #e7e7e7;
                font-weight: bold;
            }
            .invoice-total {
                text-align: right;
                font-size: 18px;
                margin-top: 20px;
                color: #f47a45;
                font-weight: bold;
                padding-top: 10px;
                border-top: 2px solid #f47a45;
            }
        ';
    }

    /**
     * AJAX handler for getting design preview
     *
     * @since 1.0.0
     */
    public function si_ajax_get_design_preview() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_design_preview_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $design_id = si_sanitize_text($_POST['design_id'] ?? '');

        if (empty($design_id)) {
            si_send_json_response(false, __('Invalid design ID.', 'simple-invoice'));
        }

        $design = $this->si_get_design($design_id);

        if ($design) {
            si_send_json_response(
                true,
                __('Design preview retrieved.', 'simple-invoice'),
                array('design' => $design)
            );
        } else {
            si_send_json_response(false, __('Design not found.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for design preview iframe
     *
     * @since 1.0.0
     */
    public function si_ajax_preview_design() {
        // Verify nonce
        if (!wp_verify_nonce($_GET['_wpnonce'] ?? '', 'si_preview_design')) {
            wp_die(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'simple-invoice'));
        }

        $design_id = sanitize_text_field($_GET['design_id'] ?? '');

        if (empty($design_id)) {
            wp_die(__('Invalid design ID.', 'simple-invoice'));
        }

        // Get sample data for preview
        $sample_data = $this->si_get_sample_invoice_data();

        // Load the design template with sample data
        $html = $this->si_load_design_template($design_id, $sample_data);

        // Output the HTML
        echo $html;
        exit;
    }

    /**
     * Get sample invoice data for preview
     *
     * @return array Sample invoice data
     * @since 1.0.0
     */
    private function si_get_sample_invoice_data() {
        $settings = si_get_settings();

        return array(
            'invoice_number' => 'INV-2024-001',
            'invoice_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'client' => array(
                'name' => 'Sample Client Company',
                'email' => '<EMAIL>',
                'address' => "123 Business Street\nSuite 100\nCity, State 12345\nCountry"
            ),
            'template' => array(
                'name' => 'Sample Template',
                'description' => 'This is a sample template for preview'
            ),
            'items' => array(
                array(
                    'description' => 'Web Design Services',
                    'quantity' => 1,
                    'rate' => '$1,500.00',
                    'total' => '$1,500.00'
                ),
                array(
                    'description' => 'Development Hours',
                    'quantity' => 20,
                    'rate' => '$75.00',
                    'total' => '$1,500.00'
                ),
                array(
                    'description' => 'Consultation',
                    'quantity' => 5,
                    'rate' => '$100.00',
                    'total' => '$500.00'
                )
            ),
            'subtotal' => '$3,500.00',
            'tax_amount' => '$350.00',
            'discount_amount' => '$0.00',
            'shipping_amount' => '$0.00',
            'total_amount' => '$3,850.00',
            'settings' => $settings
        );
    }

    /**
     * Validate design template
     *
     * @param string $design_id Design ID
     * @return bool True if valid, false otherwise
     * @since 1.0.0
     */
    public function si_validate_design($design_id) {
        $design = $this->si_get_design($design_id);
        return $design !== null;
    }

    /**
     * Get design template variables
     *
     * @param string $design_id Design ID
     * @return array Available template variables
     * @since 1.0.0
     */
    public function si_get_design_variables($design_id) {
        return array(
            'settings' => 'Plugin settings array',
            'client' => 'Client information object',
            'template' => 'Template configuration object',
            'invoice_number' => 'Generated invoice number',
            'invoice_date' => 'Invoice creation date',
            'due_date' => 'Payment due date',
            'items' => 'Array of invoice items',
            'subtotal' => 'Subtotal amount',
            'tax_amount' => 'Tax amount',
            'discount_amount' => 'Discount amount',
            'shipping_amount' => 'Shipping amount',
            'total_amount' => 'Final total amount',
            'upi_qr_code' => 'UPI QR code data (if enabled)'
        );
    }


}
