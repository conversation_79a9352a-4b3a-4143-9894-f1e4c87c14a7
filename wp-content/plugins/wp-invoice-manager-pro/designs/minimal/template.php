<?php
/**
 * Minimal Invoice Design Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables for use in template
$business_name = $settings['business_name'] ?? get_bloginfo('name');
$business_logo = $settings['business_logo'] ?? '';
$business_address = $settings['business_address'] ?? '';
$business_email = $settings['business_email'] ?? '';
$business_phone = $settings['business_phone'] ?? '';
$gstin = $settings['gstin'] ?? '';
$upi_id = $settings['upi_id'] ?? '';

$client_name = $client->name ?? '';
$client_business = $client->business_name ?? '';
$client_address = $client->address ?? '';
$client_email = $client->email ?? '';
$client_phone = $client->contact_number ?? '';
$client_gstin = $client->gstin ?? '';

$invoice_items = $items ?? array();
$invoice_subtotal = $subtotal ?? 0;
$invoice_tax_rate = $tax_rate ?? 0;
$invoice_tax_amount = $tax_amount ?? 0;
$invoice_discount = $discount_amount ?? 0;
$invoice_shipping = $shipping_amount ?? 0;
$invoice_total = $total_amount ?? 0;
$invoice_notes = $notes ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html__('Invoice', 'simple-invoice'); ?> - <?php echo esc_html($invoice_number); ?></title>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 40px;
            color: #000000;
            line-height: 1.6;
            background: #ffffff;
            font-size: 14px;
        }

        .invoice-container {
            max-width: 700px;
            margin: 0 auto;
            background: #ffffff;
        }

        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 60px;
            padding-bottom: 30px;
            border-bottom: 1px solid #e7e7e7;
        }
        
        .business-info {
            flex: 1;
        }
        
        .business-logo {
            margin-bottom: 20px;
        }
        
        .business-logo img {
            max-width: 150px;
            height: auto;
        }
        
        .business-name {
            font-size: 24px;
            font-weight: 300;
            color: #f47a45;
            margin: 0 0 15px 0;
            letter-spacing: -0.5px;
        }

        .business-details {
            font-size: 13px;
            color: #5f5f5f;
            line-height: 1.8;
        }
        
        .invoice-title {
            text-align: right;
            flex: 0 0 auto;
        }
        
        .invoice-title h1 {
            font-size: 32px;
            font-weight: 100;
            color: #f47a45;
            margin: 0;
            letter-spacing: 2px;
        }
        
        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 50px;
        }
        
        .invoice-details,
        .client-details {
            flex: 1;
        }
        
        .invoice-details {
            margin-right: 60px;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: 600;
            color: #5f5f5f;
            margin: 0 0 15px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .detail-item {
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .detail-label {
            color: #5f5f5f;
            display: inline-block;
            width: 80px;
            font-weight: 500;
        }

        .detail-value {
            color: #000000;
        }

        .client-name {
            font-size: 16px;
            font-weight: 500;
            color: #000000;
            margin-bottom: 10px;
        }

        .client-info {
            font-size: 13px;
            color: #5f5f5f;
            line-height: 1.6;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 40px 0;
        }
        
        .items-table th {
            background: none;
            color: #5f5f5f;
            font-weight: 600;
            padding: 15px 0;
            text-align: left;
            border-bottom: 1px solid #e7e7e7;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .items-table td {
            padding: 20px 0;
            border-bottom: 1px solid #e7e7e7;
            font-size: 13px;
            color: #000000;
        }
        
        .items-table tr:last-child td {
            border-bottom: none;
        }
        
        .items-table .text-right {
            text-align: right;
        }
        
        .items-table .text-center {
            text-align: center;
        }
        
        .items-table .item-description {
            font-weight: 500;
        }
        
        .summary-section {
            margin-top: 50px;
            display: flex;
            justify-content: flex-end;
        }
        
        .summary-table {
            width: 250px;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 8px 0;
            font-size: 13px;
            border: none;
        }
        
        .summary-table .summary-label {
            color: #5f5f5f;
            text-align: right;
            padding-right: 20px;
            font-weight: 500;
        }

        .summary-table .summary-value {
            text-align: right;
            color: #000000;
            font-weight: 500;
        }

        .summary-table .total-row {
            border-top: 1px solid #e7e7e7;
            margin-top: 10px;
        }

        .summary-table .total-row td {
            font-size: 16px;
            font-weight: 600;
            color: #f47a45;
            padding: 15px 0 5px 0;
        }
        
        .payment-info {
            margin-top: 60px;
            padding: 30px 0;
            border-top: 1px solid #e7e7e7;
        }
        
        .payment-info .section-title {
            margin-bottom: 25px;
        }
        
        .payment-methods {
            display: flex;
            gap: 40px;
            flex-wrap: wrap;
        }
        
        .payment-method {
            flex: 1;
            min-width: 200px;
        }
        
        .payment-method h4 {
            font-size: 13px;
            font-weight: 600;
            color: #000000;
            margin: 0 0 10px 0;
        }

        .payment-method p {
            margin: 0;
            font-size: 12px;
            color: #5f5f5f;
            line-height: 1.6;
        }
        
        .upi-qr {
            text-align: center;
            margin-top: 20px;
        }
        
        .upi-qr img {
            max-width: 100px;
            height: auto;
            border: 1px solid #e7e7e7;
            padding: 5px;
            background: #ffffff;
        }

        .upi-qr p {
            margin: 8px 0 0 0;
            font-size: 11px;
            color: #5f5f5f;
        }
        
        .notes-section {
            margin-top: 60px;
            padding-top: 30px;
            border-top: 1px solid #e7e7e7;
        }
        
        .note-item {
            margin-bottom: 30px;
        }
        
        .note-item:last-child {
            margin-bottom: 0;
        }
        
        .note-item .section-title {
            margin-bottom: 10px;
        }
        
        .note-item p {
            margin: 0;
            font-size: 12px;
            color: #5f5f5f;
            line-height: 1.7;
        }

        .footer {
            margin-top: 80px;
            padding-top: 20px;
            border-top: 1px solid #e7e7e7;
            text-align: center;
            font-size: 11px;
            color: #5f5f5f;
        }
        
        @media print {
            body {
                padding: 20px;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @media (max-width: 600px) {
            body {
                padding: 20px;
            }
            
            .invoice-header {
                flex-direction: column;
                text-align: center;
            }
            
            .invoice-title {
                text-align: center;
                margin-top: 30px;
            }
            
            .invoice-meta {
                flex-direction: column;
            }
            
            .invoice-details {
                margin-right: 0;
                margin-bottom: 30px;
            }
            
            .payment-methods {
                flex-direction: column;
                gap: 20px;
            }
            
            .summary-section {
                justify-content: center;
            }
            
            .summary-table {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="business-info">
                <?php if (!empty($business_logo)): ?>
                    <div class="business-logo">
                        <img src="<?php echo esc_url($business_logo); ?>" alt="<?php echo esc_attr($business_name); ?>" />
                    </div>
                <?php endif; ?>
                
                <h2 class="business-name"><?php echo esc_html($business_name); ?></h2>
                
                <div class="business-details">
                    <?php if (!empty($business_address)): ?>
                        <div><?php echo nl2br(esc_html($business_address)); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($business_email)): ?>
                        <div><?php echo esc_html($business_email); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($business_phone)): ?>
                        <div><?php echo esc_html($business_phone); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($gstin)): ?>
                        <div><?php echo esc_html__('GSTIN:', 'simple-invoice'); ?> <?php echo esc_html($gstin); ?></div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="invoice-title">
                <h1><?php echo esc_html__('INVOICE', 'simple-invoice'); ?></h1>
            </div>
        </div>
        
        <!-- Invoice and Client Details -->
        <div class="invoice-meta">
            <div class="invoice-details">
                <h3 class="section-title"><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h3>
                <div class="detail-item">
                    <span class="detail-label"><?php echo esc_html__('Number', 'simple-invoice'); ?></span>
                    <span class="detail-value"><?php echo esc_html($invoice_number); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><?php echo esc_html__('Date', 'simple-invoice'); ?></span>
                    <span class="detail-value"><?php echo esc_html($invoice_date); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><?php echo esc_html__('Due', 'simple-invoice'); ?></span>
                    <span class="detail-value"><?php echo esc_html($due_date); ?></span>
                </div>
            </div>
            
            <div class="client-details">
                <h3 class="section-title"><?php echo esc_html__('Bill To', 'simple-invoice'); ?></h3>
                <div class="client-name"><?php echo esc_html($client_name); ?></div>
                
                <div class="client-info">
                    <?php if (!empty($client_business)): ?>
                        <div><?php echo esc_html($client_business); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_address)): ?>
                        <div><?php echo nl2br(esc_html($client_address)); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_email)): ?>
                        <div><?php echo esc_html($client_email); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_phone)): ?>
                        <div><?php echo esc_html($client_phone); ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_gstin)): ?>
                        <div><?php echo esc_html__('GSTIN:', 'simple-invoice'); ?> <?php echo esc_html($client_gstin); ?></div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Items Table -->
        <?php
        // Get dynamic fields from template or use defaults
        $table_fields = array();
        if (isset($template) && !empty($template->body_fields)) {
            $table_fields = $template->body_fields;
        }

        // If no fields defined, use default structure
        if (empty($table_fields)) {
            $table_fields = array(
                array('name' => 'sr_no', 'label' => 'Sr. No', 'type' => 'serial'),
                array('name' => 'description', 'label' => 'Description', 'type' => 'text'),
                array('name' => 'quantity', 'label' => 'Qty', 'type' => 'number'),
                array('name' => 'rate', 'label' => 'Rate', 'type' => 'currency'),
                array('name' => 'total', 'label' => 'Amount', 'type' => 'calculated', 'formula' => 'quantity * rate')
            );
        }

        $field_count = count($table_fields);
        ?>
        <table class="items-table">
            <thead>
                <tr>
                    <?php foreach ($table_fields as $field): ?>
                        <th class="<?php echo in_array($field['type'], ['number', 'currency', 'calculated']) ? 'text-right' : (($field['type'] === 'serial') ? 'text-center' : ''); ?>">
                            <?php echo esc_html($field['label']); ?>
                        </th>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($invoice_items)): ?>
                    <?php foreach ($invoice_items as $item_index => $item): ?>
                        <tr>
                            <?php foreach ($table_fields as $field): ?>
                                <td class="<?php echo in_array($field['type'], ['number', 'currency', 'calculated']) ? 'text-right' : (($field['type'] === 'serial') ? 'text-center' : ''); ?><?php echo ($field['type'] === 'text' && $field['name'] === 'description') ? ' item-description' : ''; ?>">
                                    <?php
                                    switch ($field['type']) {
                                        case 'serial':
                                            echo esc_html($item_index + 1);
                                            break;

                                        case 'text':
                                            $value = $item[$field['name']] ?? '';
                                            // Fallback for legacy data structure
                                            if (empty($value) && $field['name'] === 'description') {
                                                $value = $item['description'] ?? $item[1] ?? '';
                                            }
                                            echo esc_html($value);
                                            break;

                                        case 'number':
                                            $value = $item[$field['name']] ?? 0;
                                            // Fallback for legacy data structure
                                            if (empty($value) && $field['name'] === 'quantity') {
                                                $value = $item['quantity'] ?? $item[2] ?? 0;
                                            }
                                            echo esc_html($value);
                                            break;

                                        case 'currency':
                                            $value = $item[$field['name']] ?? 0;
                                            // Fallback for legacy data structure
                                            if (empty($value) && $field['name'] === 'rate') {
                                                $value = $item['rate'] ?? $item[3] ?? 0;
                                            }
                                            echo si_format_currency($value);
                                            break;

                                        case 'calculated':
                                            if (isset($field['formula']) && $field['formula'] === 'quantity * rate') {
                                                $quantity = $item['quantity'] ?? $item[2] ?? 0;
                                                $rate = $item['rate'] ?? $item[3] ?? 0;
                                                echo si_format_currency($quantity * $rate);
                                            } else {
                                                // For other calculated fields, try to get the value directly
                                                $value = $item[$field['name']] ?? 0;
                                                echo si_format_currency($value);
                                            }
                                            break;

                                        default:
                                            // For custom field types, display as text
                                            $value = $item[$field['name']] ?? '';
                                            echo esc_html($value);
                                            break;
                                    }
                                    ?>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?php echo esc_attr($field_count); ?>" style="text-align: center; color: #bdc3c7; font-style: italic; padding: 40px 0;">
                            <?php echo esc_html__('No items found.', 'simple-invoice'); ?>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- Summary -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="summary-label"><?php echo esc_html__('Subtotal', 'simple-invoice'); ?></td>
                    <td class="summary-value"><?php echo si_format_currency($invoice_subtotal); ?></td>
                </tr>
                
                <?php if ($invoice_tax_rate > 0): ?>
                    <tr>
                        <td class="summary-label"><?php echo esc_html__('Tax', 'simple-invoice'); ?> (<?php echo esc_html($invoice_tax_rate); ?>%)</td>
                        <td class="summary-value"><?php echo si_format_currency($invoice_tax_amount); ?></td>
                    </tr>
                <?php endif; ?>
                
                <?php if ($invoice_discount > 0): ?>
                    <tr>
                        <td class="summary-label"><?php echo esc_html__('Discount', 'simple-invoice'); ?></td>
                        <td class="summary-value">-<?php echo si_format_currency($invoice_discount); ?></td>
                    </tr>
                <?php endif; ?>
                
                <?php if ($invoice_shipping > 0): ?>
                    <tr>
                        <td class="summary-label"><?php echo esc_html__('Shipping', 'simple-invoice'); ?></td>
                        <td class="summary-value"><?php echo si_format_currency($invoice_shipping); ?></td>
                    </tr>
                <?php endif; ?>
                
                <tr class="total-row">
                    <td class="summary-label"><?php echo esc_html__('Total', 'simple-invoice'); ?></td>
                    <td class="summary-value"><?php echo si_format_currency($invoice_total); ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Payment Information -->
        <?php if (!empty($settings['payment_methods'])): ?>
            <div class="payment-info">
                <h3 class="section-title"><?php echo esc_html__('Payment Information', 'simple-invoice'); ?></h3>
                
                <div class="payment-methods">
                    <?php if (in_array('bank', $settings['payment_methods']) && !empty($settings['bank_details'])): ?>
                        <div class="payment-method">
                            <h4><?php echo esc_html__('Bank Transfer', 'simple-invoice'); ?></h4>
                            <p><?php echo nl2br(esc_html($settings['bank_details'])); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (in_array('upi', $settings['payment_methods']) && !empty($upi_id)): ?>
                        <div class="payment-method">
                            <h4><?php echo esc_html__('UPI Payment', 'simple-invoice'); ?></h4>
                            <p><?php echo esc_html__('UPI ID:', 'simple-invoice'); ?> <?php echo esc_html($upi_id); ?></p>
                            
                            <?php if (!empty($upi_qr_code)): ?>
                                <div class="upi-qr">
                                    <img src="<?php echo esc_url($upi_qr_code); ?>" alt="<?php echo esc_attr__('UPI QR Code', 'simple-invoice'); ?>" />
                                    <p><?php echo esc_html__('Scan to pay', 'simple-invoice'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Notes -->
        <?php if (!empty($invoice_notes) || !empty($settings['footer_notes']) || !empty($settings['terms_text'])): ?>
            <div class="notes-section">
                <?php if (!empty($invoice_notes)): ?>
                    <div class="note-item">
                        <h3 class="section-title"><?php echo esc_html__('Notes', 'simple-invoice'); ?></h3>
                        <p><?php echo nl2br(esc_html($invoice_notes)); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($settings['footer_notes'])): ?>
                    <div class="note-item">
                        <h3 class="section-title"><?php echo esc_html__('Thank You', 'simple-invoice'); ?></h3>
                        <p><?php echo nl2br(esc_html($settings['footer_notes'])); ?></p>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($settings['terms_text'])): ?>
                    <div class="note-item">
                        <h3 class="section-title"><?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?></h3>
                        <p><?php echo nl2br(esc_html($settings['terms_text'])); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Footer -->
        <div class="footer">
            <p><?php echo esc_html__('This is a computer-generated invoice.', 'simple-invoice'); ?></p>
        </div>
    </div>
</body>
</html>
