<?php
/**
 * Modern Invoice Design Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Extract variables for use in template
$business_name = $settings['business_name'] ?? get_bloginfo('name');
$business_logo = $settings['business_logo'] ?? '';
$business_address = $settings['business_address'] ?? '';
$business_email = $settings['business_email'] ?? '';
$business_phone = $settings['business_phone'] ?? '';
$gstin = $settings['gstin'] ?? '';
$upi_id = $settings['upi_id'] ?? '';

$client_name = $client->name ?? '';
$client_business = $client->business_name ?? '';
$client_address = $client->address ?? '';
$client_email = $client->email ?? '';
$client_phone = $client->contact_number ?? '';
$client_gstin = $client->gstin ?? '';

$invoice_items = $items ?? array();
$invoice_subtotal = $subtotal ?? 0;
$invoice_tax_rate = $tax_rate ?? 0;
$invoice_tax_amount = $tax_amount ?? 0;
$invoice_discount = $discount_amount ?? 0;
$invoice_shipping = $shipping_amount ?? 0;
$invoice_total = $total_amount ?? 0;
$invoice_notes = $notes ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html__('Invoice', 'simple-invoice'); ?> - <?php echo esc_html($invoice_number); ?></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            color: #000000;
            line-height: 1.6;
            background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
            min-height: 100vh;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .invoice-header {
            background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .invoice-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(45deg);
        }
        
        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .business-info {
            flex: 1;
        }
        
        .business-logo {
            margin-bottom: 20px;
        }
        
        .business-logo img {
            max-width: 180px;
            height: auto;
            filter: brightness(0) invert(1);
        }
        
        .business-name {
            font-size: 32px;
            font-weight: 700;
            margin: 0 0 12px 0;
            letter-spacing: -0.025em;
        }
        
        .business-details {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .invoice-title {
            text-align: right;
            flex: 0 0 auto;
        }
        
        .invoice-title h1 {
            font-size: 48px;
            font-weight: 300;
            margin: 0;
            letter-spacing: -0.05em;
            opacity: 0.9;
        }
        
        .invoice-body {
            padding: 40px;
        }
        
        .invoice-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .detail-card {
            background: #e7e7e7;
            border-radius: 12px;
            padding: 24px;
            border-left: 4px solid #f47a45;
        }

        .detail-card h3 {
            font-size: 14px;
            font-weight: 600;
            color: #f47a45;
            margin: 0 0 16px 0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .detail-item {
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .detail-label {
            font-weight: 500;
            color: #000000;
            display: inline-block;
            width: 100px;
        }

        .detail-value {
            color: #5f5f5f;
        }

        .client-name {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 8px;
        }
        
        .items-section {
            margin: 40px 0;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .items-table th {
            background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
            color: white;
            font-weight: 600;
            padding: 20px 16px;
            text-align: left;
            font-size: 14px;
            letter-spacing: 0.025em;
        }
        
        .items-table td {
            padding: 16px;
            border-bottom: 1px solid #e7e7e7;
            font-size: 14px;
            background: white;
        }

        .items-table tr:last-child td {
            border-bottom: none;
        }

        .items-table tr:nth-child(even) td {
            background: #e7e7e7;
        }
        
        .items-table .text-right {
            text-align: right;
        }
        
        .items-table .text-center {
            text-align: center;
        }
        
        .summary-section {
            margin-top: 40px;
            display: flex;
            justify-content: flex-end;
        }
        
        .summary-card {
            background: #e7e7e7;
            border-radius: 12px;
            padding: 24px;
            min-width: 320px;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 8px 0;
            font-size: 14px;
            border-bottom: 1px solid #e7e7e7;
        }

        .summary-table .summary-label {
            font-weight: 500;
            color: #000000;
            text-align: left;
        }

        .summary-table .summary-value {
            text-align: right;
            color: #5f5f5f;
            font-weight: 500;
        }
        
        .summary-table .total-row {
            border-top: 2px solid #f47a45;
            border-bottom: none;
        }

        .summary-table .total-row td {
            font-size: 18px;
            font-weight: 700;
            color: #f47a45;
            padding: 16px 0 8px 0;
        }
        
        .payment-section {
            margin-top: 40px;
            background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
            border-radius: 12px;
            padding: 32px;
        }

        .payment-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #000000;
            margin: 0 0 20px 0;
        }
        
        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }
        
        .payment-method {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e7e7e7;
        }

        .payment-method h4 {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
            margin: 0 0 12px 0;
        }

        .payment-method p {
            margin: 0;
            font-size: 13px;
            color: #5f5f5f;
            line-height: 1.5;
        }
        
        .upi-qr {
            text-align: center;
            margin-top: 16px;
        }
        
        .upi-qr img {
            max-width: 120px;
            height: auto;
            border-radius: 8px;
            border: 1px solid #e7e7e7;
            padding: 8px;
            background: white;
        }

        .upi-qr p {
            margin: 8px 0 0 0;
            font-size: 12px;
            color: #5f5f5f;
        }
        
        .notes-section {
            margin-top: 40px;
            display: grid;
            gap: 24px;
        }
        
        .note-card {
            background: #e7e7e7;
            border-radius: 12px;
            padding: 24px;
            border-left: 4px solid #f47a45;
        }

        .note-card h3 {
            font-size: 16px;
            font-weight: 600;
            color: #f47a45;
            margin: 0 0 12px 0;
        }

        .note-card p {
            margin: 0;
            font-size: 14px;
            color: #000000;
            line-height: 1.6;
        }
        
        .footer {
            margin-top: 40px;
            padding: 24px 40px;
            background: #e7e7e7;
            text-align: center;
            font-size: 12px;
            color: #5f5f5f;
            border-top: 1px solid #e7e7e7;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .invoice-container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .invoice-header {
                padding: 24px;
            }
            
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            .invoice-title {
                text-align: center;
                margin-top: 20px;
            }
            
            .invoice-body {
                padding: 24px;
            }
            
            .invoice-meta {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .payment-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-section {
                justify-content: center;
            }
            
            .summary-card {
                min-width: auto;
                width: 100%;
            }
            
            .footer {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="header-content">
                <div class="business-info">
                    <?php if (!empty($business_logo)): ?>
                        <div class="business-logo">
                            <img src="<?php echo esc_url($business_logo); ?>" alt="<?php echo esc_attr($business_name); ?>" />
                        </div>
                    <?php endif; ?>
                    
                    <h2 class="business-name"><?php echo esc_html($business_name); ?></h2>
                    
                    <div class="business-details">
                        <?php if (!empty($business_address)): ?>
                            <div><?php echo nl2br(esc_html($business_address)); ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($business_email)): ?>
                            <div><?php echo esc_html($business_email); ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($business_phone)): ?>
                            <div><?php echo esc_html($business_phone); ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($gstin)): ?>
                            <div><?php echo esc_html__('GSTIN:', 'simple-invoice'); ?> <?php echo esc_html($gstin); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="invoice-title">
                    <h1><?php echo esc_html__('INVOICE', 'simple-invoice'); ?></h1>
                </div>
            </div>
        </div>
        
        <div class="invoice-body">
            <!-- Invoice and Client Details -->
            <div class="invoice-meta">
                <div class="detail-card">
                    <h3><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h3>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo esc_html__('Number:', 'simple-invoice'); ?></span>
                        <span class="detail-value"><?php echo esc_html($invoice_number); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo esc_html__('Date:', 'simple-invoice'); ?></span>
                        <span class="detail-value"><?php echo esc_html($invoice_date); ?></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label"><?php echo esc_html__('Due Date:', 'simple-invoice'); ?></span>
                        <span class="detail-value"><?php echo esc_html($due_date); ?></span>
                    </div>
                </div>
                
                <div class="detail-card">
                    <h3><?php echo esc_html__('Bill To', 'simple-invoice'); ?></h3>
                    <div class="client-name"><?php echo esc_html($client_name); ?></div>
                    
                    <?php if (!empty($client_business)): ?>
                        <div class="detail-item">
                            <span class="detail-value"><?php echo esc_html($client_business); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_address)): ?>
                        <div class="detail-item">
                            <span class="detail-value"><?php echo nl2br(esc_html($client_address)); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_email)): ?>
                        <div class="detail-item">
                            <span class="detail-value"><?php echo esc_html($client_email); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_phone)): ?>
                        <div class="detail-item">
                            <span class="detail-value"><?php echo esc_html($client_phone); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($client_gstin)): ?>
                        <div class="detail-item">
                            <span class="detail-label"><?php echo esc_html__('GSTIN:', 'simple-invoice'); ?></span>
                            <span class="detail-value"><?php echo esc_html($client_gstin); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Items Table -->
            <?php
            // Get dynamic fields from template or use defaults
            $table_fields = array();
            if (isset($template) && !empty($template->body_fields)) {
                $table_fields = $template->body_fields;
            }

            // If no fields defined, use default structure
            if (empty($table_fields)) {
                $table_fields = array(
                    array('name' => 'sr_no', 'label' => 'Sr. No', 'type' => 'serial'),
                    array('name' => 'description', 'label' => 'Description', 'type' => 'text'),
                    array('name' => 'quantity', 'label' => 'Qty', 'type' => 'number'),
                    array('name' => 'rate', 'label' => 'Rate', 'type' => 'currency'),
                    array('name' => 'total', 'label' => 'Amount', 'type' => 'calculated', 'formula' => 'quantity * rate')
                );
            }

            $field_count = count($table_fields);
            ?>
            <div class="items-section">
                <table class="items-table">
                    <thead>
                        <tr>
                            <?php foreach ($table_fields as $field): ?>
                                <th class="<?php echo in_array($field['type'], ['number', 'currency', 'calculated']) ? 'text-right' : (($field['type'] === 'serial') ? 'text-center' : ''); ?>">
                                    <?php echo esc_html($field['label']); ?>
                                </th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($invoice_items)): ?>
                            <?php foreach ($invoice_items as $item_index => $item): ?>
                                <tr>
                                    <?php foreach ($table_fields as $field): ?>
                                        <td class="<?php echo in_array($field['type'], ['number', 'currency', 'calculated']) ? 'text-right' : (($field['type'] === 'serial') ? 'text-center' : ''); ?>">
                                            <?php
                                            switch ($field['type']) {
                                                case 'serial':
                                                    echo esc_html($item_index + 1);
                                                    break;

                                                case 'text':
                                                    $value = $item[$field['name']] ?? '';
                                                    // Fallback for legacy data structure
                                                    if (empty($value) && $field['name'] === 'description') {
                                                        $value = $item['description'] ?? $item[1] ?? '';
                                                    }
                                                    echo esc_html($value);
                                                    break;

                                                case 'number':
                                                    $value = $item[$field['name']] ?? 0;
                                                    // Fallback for legacy data structure
                                                    if (empty($value) && $field['name'] === 'quantity') {
                                                        $value = $item['quantity'] ?? $item[2] ?? 0;
                                                    }
                                                    echo esc_html($value);
                                                    break;

                                                case 'currency':
                                                    $value = $item[$field['name']] ?? 0;
                                                    // Fallback for legacy data structure
                                                    if (empty($value) && $field['name'] === 'rate') {
                                                        $value = $item['rate'] ?? $item[3] ?? 0;
                                                    }
                                                    echo si_format_currency($value);
                                                    break;

                                                case 'calculated':
                                                    if (isset($field['formula']) && $field['formula'] === 'quantity * rate') {
                                                        $quantity = $item['quantity'] ?? $item[2] ?? 0;
                                                        $rate = $item['rate'] ?? $item[3] ?? 0;
                                                        echo si_format_currency($quantity * $rate);
                                                    } else {
                                                        // For other calculated fields, try to get the value directly
                                                        $value = $item[$field['name']] ?? 0;
                                                        echo si_format_currency($value);
                                                    }
                                                    break;

                                                default:
                                                    // For custom field types, display as text
                                                    $value = $item[$field['name']] ?? '';
                                                    echo esc_html($value);
                                                    break;
                                            }
                                            ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="<?php echo esc_attr($field_count); ?>" style="text-align: center; color: #9ca3af; font-style: italic; padding: 40px;">
                                    <?php echo esc_html__('No items found.', 'simple-invoice'); ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Summary -->
            <div class="summary-section">
                <div class="summary-card">
                    <table class="summary-table">
                        <tr>
                            <td class="summary-label"><?php echo esc_html__('Subtotal', 'simple-invoice'); ?></td>
                            <td class="summary-value"><?php echo si_format_currency($invoice_subtotal); ?></td>
                        </tr>
                        
                        <?php if ($invoice_tax_rate > 0): ?>
                            <tr>
                                <td class="summary-label"><?php echo esc_html__('Tax', 'simple-invoice'); ?> (<?php echo esc_html($invoice_tax_rate); ?>%)</td>
                                <td class="summary-value"><?php echo si_format_currency($invoice_tax_amount); ?></td>
                            </tr>
                        <?php endif; ?>
                        
                        <?php if ($invoice_discount > 0): ?>
                            <tr>
                                <td class="summary-label"><?php echo esc_html__('Discount', 'simple-invoice'); ?></td>
                                <td class="summary-value">-<?php echo si_format_currency($invoice_discount); ?></td>
                            </tr>
                        <?php endif; ?>
                        
                        <?php if ($invoice_shipping > 0): ?>
                            <tr>
                                <td class="summary-label"><?php echo esc_html__('Shipping', 'simple-invoice'); ?></td>
                                <td class="summary-value"><?php echo si_format_currency($invoice_shipping); ?></td>
                            </tr>
                        <?php endif; ?>
                        
                        <tr class="total-row">
                            <td class="summary-label"><?php echo esc_html__('Total', 'simple-invoice'); ?></td>
                            <td class="summary-value"><?php echo si_format_currency($invoice_total); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Payment Information -->
            <?php if (!empty($settings['payment_methods'])): ?>
                <div class="payment-section">
                    <h3><?php echo esc_html__('Payment Information', 'simple-invoice'); ?></h3>
                    
                    <div class="payment-grid">
                        <?php if (in_array('bank', $settings['payment_methods']) && !empty($settings['bank_details'])): ?>
                            <div class="payment-method">
                                <h4><?php echo esc_html__('Bank Transfer', 'simple-invoice'); ?></h4>
                                <p><?php echo nl2br(esc_html($settings['bank_details'])); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (in_array('upi', $settings['payment_methods']) && !empty($upi_id)): ?>
                            <div class="payment-method">
                                <h4><?php echo esc_html__('UPI Payment', 'simple-invoice'); ?></h4>
                                <p><?php echo esc_html__('UPI ID:', 'simple-invoice'); ?> <?php echo esc_html($upi_id); ?></p>
                                
                                <?php if (!empty($upi_qr_code)): ?>
                                    <div class="upi-qr">
                                        <img src="<?php echo esc_url($upi_qr_code); ?>" alt="<?php echo esc_attr__('UPI QR Code', 'simple-invoice'); ?>" />
                                        <p><?php echo esc_html__('Scan to pay', 'simple-invoice'); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Notes -->
            <?php if (!empty($invoice_notes) || !empty($settings['footer_notes']) || !empty($settings['terms_text'])): ?>
                <div class="notes-section">
                    <?php if (!empty($invoice_notes)): ?>
                        <div class="note-card">
                            <h3><?php echo esc_html__('Notes', 'simple-invoice'); ?></h3>
                            <p><?php echo nl2br(esc_html($invoice_notes)); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($settings['footer_notes'])): ?>
                        <div class="note-card">
                            <h3><?php echo esc_html__('Thank You', 'simple-invoice'); ?></h3>
                            <p><?php echo nl2br(esc_html($settings['footer_notes'])); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($settings['terms_text'])): ?>
                        <div class="note-card">
                            <h3><?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?></h3>
                            <p><?php echo nl2br(esc_html($settings['terms_text'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><?php echo esc_html__('This is a computer-generated invoice.', 'simple-invoice'); ?></p>
        </div>
    </div>
</body>
</html>
