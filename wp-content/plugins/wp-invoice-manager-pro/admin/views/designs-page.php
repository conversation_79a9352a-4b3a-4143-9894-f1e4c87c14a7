<?php
/**
 * Designs Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Remove WordPress admin footer for this page
add_filter('admin_footer_text', '__return_empty_string', 11);
add_filter('update_footer', '__return_empty_string', 11);

// Add custom CSS to hide footer completely
add_action('admin_head', function() {
    echo '<style type="text/css">
        #wpfooter {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
        }
        #wpbody-content {
            padding-bottom: 60px !important;
        }
    </style>';
});

// Get design loader
$design_loader = new SI_Design_Loader();
$all_designs = $design_loader->si_get_available_designs();

// Filter out minimal and modern designs as requested
$available_designs = array_filter($all_designs, function($design_id) {
    return !in_array($design_id, ['minimal', 'modern']);
}, ARRAY_FILTER_USE_KEY);

// Set up page header variables
$page_title = __('Invoice Designs', 'wp-invoice-manager-pro');
$page_subtitle = __('Manage visual designs for your invoices', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-admin-appearance';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add Custom Design', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary si-add-design-btn si-modal-trigger',
        'data' => array(
            'modal' => '#si-design-modal'
        )
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Statistics Overview -->
    <div class="si-dashboard-stats">
        <h2><?php echo esc_html__('Design Overview', 'simple-invoice'); ?></h2>

        <div class="si-stats-grid">
            <!-- Total Designs -->
            <div class="si-stat-card si-stat-designs">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-admin-appearance"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(count($available_designs)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Designs', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Available designs', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Built-in Designs -->
            <div class="si-stat-card si-stat-builtin">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-yes"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number">3</div>
                    <div class="si-stat-label"><?php echo esc_html__('Built-in', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Default designs', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Custom Designs -->
            <div class="si-stat-card si-stat-custom">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-star-filled"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(max(0, count($available_designs) - 3)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Custom', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('User created', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Upload Status -->
            <div class="si-stat-card si-stat-upload">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-upload"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html__('Easy', 'simple-invoice'); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Upload', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Add new designs', 'simple-invoice'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Two Column Layout -->
    <div class="si-dashboard-columns">
        <!-- Available Designs -->
        <div class="si-dashboard-column">
            <div class="si-dashboard-widget">
                <div class="si-widget-header">
                    <h3><?php echo esc_html__('Available Designs', 'simple-invoice'); ?></h3>
                    <a href="#si-design-modal" class="si-widget-action si-add-design-btn si-modal-trigger" data-modal="#si-design-modal">
                        <?php echo esc_html__('Add Custom', 'simple-invoice'); ?>
                    </a>
                </div>
                <div class="si-widget-content">
                    <?php if (!empty($available_designs)): ?>
                        <div class="si-designs-list">
                            <?php foreach ($available_designs as $design_id => $design): ?>
                                <div class="si-design-item" data-design-id="<?php echo esc_attr($design_id); ?>">
                                    <div class="si-design-info">
                                        <div class="si-design-name">
                                            <strong><?php echo esc_html($design['name']); ?></strong>
                                        </div>
                                        <div class="si-design-description"><?php echo esc_html($design['description']); ?></div>
                                        <div class="si-design-type">
                                            <span class="si-design-badge <?php echo $design_id === 'classic' ? 'si-badge-builtin' : 'si-badge-custom'; ?>">
                                                <?php echo $design_id === 'classic' ?
                                                    esc_html__('Built-in', 'simple-invoice') :
                                                    esc_html__('Custom', 'simple-invoice'); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="si-design-actions">
                                        <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-ajax.php?action=si_preview_design&design_id=' . $design_id), 'si_preview_design')); ?>" target="_blank" class="si-btn si-btn-primary si-btn-sm">
                                            <span class="dashicons dashicons-visibility"></span>
                                            <?php echo esc_html__('Preview', 'simple-invoice'); ?>
                                        </a>
                                        <?php if ($design_id !== 'classic'): ?>
                                            <button type="button" class="si-btn si-btn-secondary si-btn-sm si-edit-design" data-design-id="<?php echo esc_attr($design_id); ?>">
                                                <span class="dashicons dashicons-edit"></span>
                                                <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                            </button>
                                            <button type="button" class="si-btn si-btn-danger si-btn-sm si-delete-design" data-design-id="<?php echo esc_attr($design_id); ?>">
                                                <span class="dashicons dashicons-trash"></span>
                                                <?php echo esc_html__('Delete', 'simple-invoice'); ?>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="si-empty-state">
                            <p><?php echo esc_html__('No designs found. Add your first custom design to get started!', 'simple-invoice'); ?></p>
                            <a href="#si-design-modal" class="button button-primary si-add-design-btn si-modal-trigger" data-modal="#si-design-modal">
                                <?php echo esc_html__('Add Custom Design', 'simple-invoice'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Design Management -->
        <div class="si-dashboard-column">
            <div class="si-dashboard-widget">
                <div class="si-widget-header">
                    <h3><?php echo esc_html__('Design Management', 'simple-invoice'); ?></h3>
                </div>
                <div class="si-widget-content">
                    <div class="si-quick-actions">
                        <a href="#si-design-modal" class="si-quick-action si-add-design-btn si-modal-trigger" data-modal="#si-design-modal">
                            <span class="si-action-icon dashicons dashicons-plus-alt"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Add Custom Design', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Upload your own custom invoice design', 'simple-invoice'); ?></div>
                            </div>
                        </a>

                        <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'DESIGN-TEMPLATES-GUIDE.md'); ?>" target="_blank" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-book"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Design Guide', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Learn how to create custom designs', 'simple-invoice'); ?></div>
                            </div>
                        </a>

                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-settings')); ?>" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-admin-settings"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('Design Settings', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('Configure default design preferences', 'simple-invoice'); ?></div>
                            </div>
                        </a>

                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="si-quick-action">
                            <span class="si-action-icon dashicons dashicons-media-text"></span>
                            <div class="si-action-content">
                                <div class="si-action-title"><?php echo esc_html__('View Invoices', 'simple-invoice'); ?></div>
                                <div class="si-action-desc"><?php echo esc_html__('See how designs look on actual invoices', 'simple-invoice'); ?></div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Design Modal -->
<div id="si-design-modal" class="si-modal si-modal-large">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-design-modal-title"><?php echo esc_html__('Add Custom Design', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close" id="si-modal-close-x">&times;</button>
        </div>

        <div class="si-modal-body">
            <form id="si-design-form" enctype="multipart/form-data">
                <input type="hidden" id="si-design-id" name="design_id" value="" />

                <div class="si-design-form-sections">
                    <!-- Basic Information -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Basic Information', 'simple-invoice'); ?></h3>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="si-design-name"><?php echo esc_html__('Design Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="text"
                                           id="si-design-name"
                                           name="name"
                                           class="regular-text"
                                           required
                                           placeholder="<?php echo esc_attr__('e.g., Corporate Blue, Elegant Black', 'simple-invoice'); ?>" />
                                    <p class="description"><?php echo esc_html__('Give your design a unique name.', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-design-description"><?php echo esc_html__('Description', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <textarea id="si-design-description"
                                              name="description"
                                              class="large-text"
                                              rows="3"
                                              placeholder="<?php echo esc_attr__('Describe your design style and features...', 'simple-invoice'); ?>"></textarea>
                                    <p class="description"><?php echo esc_html__('Brief description of your design.', 'simple-invoice'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Design Files -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Design Files', 'simple-invoice'); ?></h3>
                        <p class="description"><?php echo esc_html__('Upload your design files or create them manually in the designs folder.', 'simple-invoice'); ?></p>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="si-design-template"><?php echo esc_html__('Template File', 'simple-invoice'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="file" id="si-design-template" name="template_file" accept=".php" />
                                    <p class="description"><?php echo esc_html__('Upload template.php file (required)', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-design-style"><?php echo esc_html__('Style File', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <input type="file" id="si-design-style" name="style_file" accept=".css" />
                                    <p class="description"><?php echo esc_html__('Upload style.css file (optional)', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-design-preview"><?php echo esc_html__('Preview Image', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <input type="file" id="si-design-preview" name="preview_file" accept=".jpg,.jpeg,.png" />
                                    <p class="description"><?php echo esc_html__('Upload preview image (optional, recommended size: 400x300px)', 'simple-invoice'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </form>
        </div>

        <div class="si-modal-footer">
            <button type="button" class="button button-primary" id="si-save-design"><?php echo esc_html__('Save Changes', 'simple-invoice'); ?></button>
            <button type="button" class="button button-secondary" id="si-close-modal-btn"><?php echo esc_html__('Close', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<style>
/* Dashboard-style Design Page Styling */

/* Design List Styling */
.si-designs-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.si-design-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid var(--light-border-color);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    background-color: var(--background-color);
}

.si-design-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.si-design-info {
    flex: 1;
}

.si-design-name {
    margin-bottom: 4px;
    font-size: 16px;
    color: var(--text-main-color);
}

.si-design-description {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 8px;
}

.si-design-type {
    margin-bottom: 0;
}

.si-design-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.si-badge-builtin {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.si-badge-custom {
    background-color: var(--secondary-light);
    color: var(--secondary-color);
}

.si-design-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-dashboard-columns {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .si-design-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .si-design-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .si-quick-action {
        padding: 12px;
    }

    .si-action-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }
}

/* Modal Styling - Enhanced System */
.si-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0,0,0,0.8) !important;
    z-index: 999999 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(8px) !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.3s ease, visibility 0.3s ease !important;
}

/* Show states - multiple selectors for compatibility */
.si-modal.show,
.si-modal.si-modal-show,
.si-modal[style*="display: flex"],
.si-modal[style*="display:flex"] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.si-modal-content {
    background: #ffffff;
    border-radius: 8px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 25px 80px rgba(0,0,0,0.4);
    animation: modalSlideIn 0.4s ease;
    margin: auto;
    position: relative;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.si-modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid #e7e7e7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f47a45 0%, #e06a35 100%);
    color: #ffffff;
    border-radius: 8px 8px 0 0;
}

.si-modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
}

.si-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #ffffff;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
    position: relative;
    z-index: 1001;
    line-height: 1;
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.si-modal-body {
    padding: 30px;
}

.si-modal-footer {
    padding: 25px 30px;
    border-top: 1px solid #e7e7e7;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

/* Form Sections */
.si-form-section {
    margin-bottom: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #f47a45;
}

.si-form-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.si-form-section .description {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

/* File Upload Styling */
input[type="file"] {
    padding: 15px;
    border: 3px dashed #ddd;
    border-radius: 8px;
    background: #fafafa;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

input[type="file"]:hover {
    border-color: #f47a45;
    background: rgba(244, 122, 69, 0.05);
}

input[type="file"]:focus {
    outline: none;
    border-color: #f47a45;
    box-shadow: 0 0 0 3px rgba(244, 122, 69, 0.1);
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
}

/* WordPress Footer Overlap Fix - Specific for designs page */
body.admin_page_wimp-designs #wpbody-content {
    padding-bottom: 120px !important;
    min-height: calc(100vh - 160px) !important;
}

/* Hide WordPress footer on this page to prevent overlap */
body.admin_page_wimp-designs #wpfooter {
    display: none !important;
}

/* Ensure the main container has enough space */
body.admin_page_wimp-designs .si-page-container {
    margin-bottom: 60px !important;
    padding-bottom: 40px !important;
}

/* Preview Modal Styles - Removed (now opens in new tab) */


</style>

<script>
jQuery(document).ready(function($) {


    // Initialize design interactions
    initializeDesignInteractions();

    function initializeDesignInteractions() {


        // Add design modal - handle all buttons with this class
        $(document).on('click', '.si-add-design-btn', function(e) {
            e.preventDefault();

            showModal('#si-design-modal');
        });

        // Close modal handlers
        $(document).on('click', '#si-close-modal-btn, #si-modal-close-x', function(e) {
            e.preventDefault();

            closeDesignModal();
        });

        // Close modal when clicking backdrop
        $(document).on('click', '#si-design-modal', function(e) {
            if (e.target === this) {

                closeDesignModal();
            }
        });

        // Prevent modal content clicks from closing modal
        $(document).on('click', '#si-design-modal .si-modal-content', function(e) {
            e.stopPropagation();
        });

        // Close modal with Escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && $('#si-design-modal').hasClass('si-modal-show')) {
                closeDesignModal();
            }
        });

        // File upload enhancements
        $(document).on('change', 'input[type="file"]', function() {
            var fileName = $(this).val().split('\\').pop();
            if (fileName) {
                $(this).next('.description').html('<strong>Selected:</strong> ' + fileName);
            }
        });


    }

    // Close the design modal
    function closeDesignModal() {

        hideModal('#si-design-modal');
    }

    function showModal(modalId) {

        var modal = $(modalId);

        if (modal.length === 0) {
            console.error('Modal not found:', modalId);
            return;
        }

        // Enhanced modal opening - multiple approaches for maximum compatibility
        // Method 1: Remove any conflicting styles
        modal.removeAttr('style');

        // Method 2: Force display with inline styles (highest priority)
        modal.css({
            'display': 'flex',
            'opacity': '1',
            'visibility': 'visible',
            'z-index': '999999'
        });

        // Method 3: Add show classes
        modal.addClass('show si-modal-show');

        // Add body class to prevent scrolling
        $('body').addClass('modal-open si-modal-open');



        // Focus first input after modal is shown
        setTimeout(function() {
            var firstInput = modal.find('input, textarea, select').first();
            if (firstInput.length) {
                firstInput[0].focus();

            }
        }, 100);
    }

    function hideModal(modalId) {

        var modal = $(modalId);

        // Enhanced modal closing - multiple approaches for maximum compatibility
        // Method 1: Remove show classes
        modal.removeClass('show si-modal-show');

        // Method 2: Force hide with inline styles
        modal.css({
            'display': 'none',
            'opacity': '0',
            'visibility': 'hidden'
        });

        // Remove body class to restore scrolling
        $('body').removeClass('modal-open si-modal-open');



        // Reset form if it's the design modal
        if (modalId === '#si-design-modal') {
            $('#si-design-form')[0].reset();
            $('#si-design-id').val('');
            $('#si-design-modal-title').text('<?php echo esc_js(__('Add Custom Design', 'simple-invoice')); ?>');
        }
    }

    // Design Guide - now opens directly in new tab, no JavaScript needed

    // Save design with loading state
    $('#si-save-design').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.html();

        // Show loading state
        $btn.html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> Saving...').prop('disabled', true);

        // Simulate save process
        setTimeout(function() {
            $btn.html('<span class="dashicons dashicons-yes"></span> Saved!').removeClass('button-primary').addClass('button-secondary');

            setTimeout(function() {
                $btn.html(originalText).prop('disabled', false).removeClass('button-secondary').addClass('button-primary');
                hideModal('#si-design-modal');
                showNotification('Design saved successfully!', 'success');
            }, 1000);
        }, 2000);
    });

    // Preview design - now handled by direct links, no JavaScript needed

    // Delete design with confirmation
    $(document).on('click', '.si-delete-design', function() {
        var designId = $(this).data('design-id');
        var designName = $(this).closest('.si-design-item').find('.si-design-name strong').text();

        if (confirm('Are you sure you want to delete the design "' + designName + '"? This action cannot be undone.')) {
            showNotification('Design deletion functionality will be implemented', 'info');
        }
    });

    // Preview functionality removed - now opens directly in new tab



    function showNotification(message, type = 'success') {
        var bgColor = type === 'success' ? 'var(--primary-color)' : type === 'error' ? '#dc3545' : '#0073aa';
        var notificationHtml = `
        <div class="si-notification" style="
            position: fixed;
            top: 32px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: var(--border-radius-sm);
            box-shadow: var(--shadow-md);
            z-index: 100001;
            animation: slideInRight 0.3s ease;
        ">
            <span class="dashicons dashicons-${type === 'success' ? 'yes' : type === 'error' ? 'no' : 'info'}" style="margin-right: 8px;"></span>
            ${message}
        </div>`;

        $('body').append(notificationHtml);

        setTimeout(function() {
            $('.si-notification').fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    // Add CSS animations
    $('<style>').text(`
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .modal-open {
            overflow: hidden;
        }
    `).appendTo('head');

    // Make sure modal is hidden on page load
    $('#si-design-modal').css({
        'display': 'none',
        'opacity': '0',
        'visibility': 'hidden'
    }).removeClass('show si-modal-show');
    $('body').removeClass('modal-open si-modal-open');






});
</script>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
