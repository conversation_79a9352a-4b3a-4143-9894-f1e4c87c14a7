<?php
/**
 * Create Invoice Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Remove WordPress admin footer for this page
add_filter('admin_footer_text', '__return_empty_string', 11);
add_filter('update_footer', '__return_empty_string', 11);

// Add custom CSS to hide footer completely
add_action('admin_head', function() {
    echo '<style type="text/css">
        #wpfooter {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
        }
        #wpbody-content {
            padding-bottom: 60px !important;
        }
    </style>';
});

// Get clients and templates
$client_manager = new SI_Client();
$template_manager = new SI_Template();
$clients = $client_manager->si_get_clients();
$templates = $template_manager->si_get_templates();

// Get selected template if provided
$selected_template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$selected_template = null;

if ($selected_template_id > 0) {
    $selected_template = $template_manager->si_get_template($selected_template_id);
}

// Set up page header variables
$page_title = __('Create New Invoice', 'wp-invoice-manager-pro');
$page_subtitle = __('Generate professional invoices for your clients with ease', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-media-text';
$header_actions = array(
    array(
        'type' => 'link',
        'url' => admin_url('admin.php?page=wimp-invoices'),
        'text' => __('View All Invoices', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-list-view',
        'class' => 'si-btn si-btn-secondary'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <?php if (empty($templates)): ?>
        <div class="si-empty-state si-warning-state">
            <div class="si-empty-icon">
                <span class="dashicons dashicons-warning"></span>
            </div>
            <h3><?php echo esc_html__('No Invoice Layouts Found!', 'simple-invoice'); ?></h3>
            <p><?php echo esc_html__('You need to create at least one invoice layout before creating invoices. Layouts combine visual designs with field configurations to define how your invoices will look and what information they contain.', 'simple-invoice'); ?></p>
            <div class="si-help-note">
                <span class="dashicons dashicons-info"></span>
                <strong><?php echo esc_html__('What\'s the difference?', 'simple-invoice'); ?></strong><br>
                <span><?php echo esc_html__('• Designs = Visual appearance (colors, fonts, layout style)', 'simple-invoice'); ?></span><br>
                <span><?php echo esc_html__('• Layouts = Designs + Field configuration (what info to show)', 'simple-invoice'); ?></span>
            </div>
            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-templates')); ?>" class="si-btn si-btn-primary">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php echo esc_html__('Create Your First Layout', 'simple-invoice'); ?>
            </a>
        </div>
    <?php else: ?>

        <!-- Progress Indicator -->
        <div class="si-progress-indicator">
            <div class="si-progress-steps">
                <div class="si-step si-step-active" data-step="1">
                    <div class="si-step-number">1</div>
                    <div class="si-step-label"><?php echo esc_html__('Configuration', 'simple-invoice'); ?></div>
                </div>
                <div class="si-step" data-step="2">
                    <div class="si-step-number">2</div>
                    <div class="si-step-label"><?php echo esc_html__('Items', 'simple-invoice'); ?></div>
                </div>
                <div class="si-step" data-step="3">
                    <div class="si-step-number">3</div>
                    <div class="si-step-label"><?php echo esc_html__('Summary', 'simple-invoice'); ?></div>
                </div>
                <div class="si-step" data-step="4">
                    <div class="si-step-number">4</div>
                    <div class="si-step-label"><?php echo esc_html__('Notes', 'simple-invoice'); ?></div>
                </div>
            </div>
            <div class="si-progress-bar">
                <div class="si-progress-fill" style="width: 25%"></div>
            </div>
        </div>

        <form id="si-invoice-form" class="si-invoice-form si-modern-form">

            <!-- Invoice Details Section -->
            <div class="si-form-section si-invoice-details si-step-content" data-step="1">
                <div class="si-section-header">
                    <div class="si-section-icon">
                        <span class="dashicons dashicons-admin-page"></span>
                    </div>
                    <div class="si-section-content">
                        <h2><?php echo esc_html__('Invoice Configuration', 'simple-invoice'); ?></h2>
                        <p class="si-section-description"><?php echo esc_html__('Choose your invoice layout and configure the basic details', 'simple-invoice'); ?></p>
                    </div>
                    <div class="si-section-badge">
                        <span class="si-badge si-badge-primary"><?php echo esc_html__('Step 1', 'simple-invoice'); ?></span>
                    </div>
                </div>

                <div class="si-form-grid si-grid-2">
                    <div class="si-form-field">
                        <label for="si-template-select" class="si-field-label">
                            <span class="dashicons dashicons-admin-page"></span>
                            <?php echo esc_html__('Invoice Layout', 'simple-invoice'); ?>
                            <span class="required">*</span>
                        </label>
                        <div class="si-field-wrapper si-field-with-action">
                            <select id="si-template-select" name="template_id" class="si-select" required>
                                <option value=""><?php echo esc_html__('Choose an invoice layout...', 'simple-invoice'); ?></option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo esc_attr($template->id); ?>"
                                            <?php selected($template->id, $selected_template_id); ?>
                                            data-design="<?php echo esc_attr($template->design); ?>">
                                        <?php echo esc_html($template->name); ?>
                                        <?php if (!empty($template->design) && $template->design !== 'classic'): ?>
                                            (<?php echo esc_html(ucwords(str_replace('-', ' ', $template->design))); ?> Design)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="button" class="si-field-action-btn si-preview-layout-btn" id="si-preview-layout" title="<?php echo esc_attr__('Preview Layout', 'simple-invoice'); ?>" style="display: none;">
                                <span class="dashicons dashicons-visibility"></span>
                            </button>
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-select" class="si-field-label">
                            <span class="dashicons dashicons-businessperson"></span>
                            <?php echo esc_html__('Client', 'simple-invoice'); ?>
                            <span class="required">*</span>
                        </label>
                        <div class="si-field-wrapper si-field-with-action">
                            <select id="si-client-select" name="client_id" class="si-select" required>
                                <?php if (empty($clients)): ?>
                                    <option value=""><?php echo esc_html__('No clients available', 'simple-invoice'); ?></option>
                                <?php else: ?>
                                    <option value=""><?php echo esc_html__('Choose a client...', 'simple-invoice'); ?></option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?php echo esc_attr($client->id); ?>">
                                            <?php echo esc_html($client->name); ?>
                                            <?php if (!empty($client->business_name)): ?>
                                                (<?php echo esc_html($client->business_name); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <button type="button" class="si-field-action-btn si-add-client-quick" onclick="openQuickClientModal(); return false;" title="<?php echo esc_attr__('Add New Client', 'simple-invoice'); ?>">
                                <span class="dashicons dashicons-plus-alt"></span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="si-form-grid si-grid-2">
                    <div class="si-form-field">
                        <label for="si-invoice-number" class="si-field-label">
                            <span class="dashicons dashicons-tag"></span>
                            <?php echo esc_html__('Invoice Number', 'simple-invoice'); ?>
                        </label>
                        <div class="si-field-wrapper">
                            <input type="text"
                                   id="si-invoice-number"
                                   name="invoice_number"
                                   class="si-input"
                                   placeholder="<?php echo esc_attr__('Auto-generated if empty', 'simple-invoice'); ?>" />
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-invoice-date" class="si-field-label">
                            <span class="dashicons dashicons-calendar-alt"></span>
                            <?php echo esc_html__('Invoice Date', 'simple-invoice'); ?>
                        </label>
                        <div class="si-field-wrapper">
                            <input type="date"
                                   id="si-invoice-date"
                                   name="invoice_date"
                                   value="<?php echo esc_attr(date('Y-m-d')); ?>"
                                   class="si-input" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items Section -->
            <div class="si-form-section si-invoice-items si-step-content" data-step="2">
                <div class="si-section-header">
                    <div class="si-section-icon">
                        <span class="dashicons dashicons-list-view"></span>
                    </div>
                    <div class="si-section-content">
                        <h2><?php echo esc_html__('Invoice Items', 'simple-invoice'); ?></h2>
                        <p class="si-section-description"><?php echo esc_html__('Add products or services to your invoice', 'simple-invoice'); ?></p>
                    </div>
                    <div class="si-section-badge">
                        <span class="si-badge si-badge-secondary"><?php echo esc_html__('Step 2', 'simple-invoice'); ?></span>
                    </div>
                </div>

                <div class="si-invoice-items-wrapper si-enhanced-items">
                    <div class="si-items-header">
                        <div class="si-items-title">
                            <h3><?php echo esc_html__('Items & Services', 'simple-invoice'); ?></h3>
                            <span class="si-items-count" id="si-items-count">0 <?php echo esc_html__('items', 'simple-invoice'); ?></span>
                        </div>
                        <button type="button" class="si-btn si-btn-primary si-btn-enhanced" id="si-add-item">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Item', 'simple-invoice'); ?>
                            <span class="si-btn-shine"></span>
                        </button>
                    </div>

                    <div class="si-items-table-container">
                        <table class="si-invoice-items-table" id="si-invoice-items">
                            <thead>
                                <tr id="si-items-header">
                                    <th class="si-col-sr"><?php echo esc_html__('#', 'simple-invoice'); ?></th>
                                    <th class="si-col-description"><?php echo esc_html__('Description', 'simple-invoice'); ?></th>
                                    <th class="si-col-quantity"><?php echo esc_html__('Qty', 'simple-invoice'); ?></th>
                                    <th class="si-col-rate"><?php echo esc_html__('Rate', 'simple-invoice'); ?></th>
                                    <th class="si-col-total"><?php echo esc_html__('Total', 'simple-invoice'); ?></th>
                                    <th class="si-col-actions"><?php echo esc_html__('Actions', 'simple-invoice'); ?></th>
                                </tr>
                            </thead>
                            <tbody id="si-items-body">
                                <!-- Items will be added here -->
                            </tbody>
                        </table>

                        <div class="si-items-empty si-enhanced-empty" id="si-items-empty" style="display: none;">
                            <div class="si-empty-animation">
                                <div class="si-empty-icon">
                                    <span class="dashicons dashicons-cart"></span>
                                </div>
                                <div class="si-empty-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                            <h4><?php echo esc_html__('No items added yet', 'simple-invoice'); ?></h4>
                            <p><?php echo esc_html__('Click "Add Item" to start building your invoice', 'simple-invoice'); ?></p>
                            <button type="button" class="si-btn si-btn-ghost si-add-first-item">
                                <span class="dashicons dashicons-plus-alt"></span>
                                <?php echo esc_html__('Add Your First Item', 'simple-invoice'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Summary Section -->
            <div class="si-form-section si-invoice-summary-section si-step-content" data-step="3">
                <div class="si-section-header">
                    <div class="si-section-icon">
                        <span class="dashicons dashicons-calculator"></span>
                    </div>
                    <div class="si-section-content">
                        <h2><?php echo esc_html__('Invoice Summary', 'simple-invoice'); ?></h2>
                        <p class="si-section-description"><?php echo esc_html__('Review and adjust the invoice totals', 'simple-invoice'); ?></p>
                    </div>
                    <div class="si-section-badge">
                        <span class="si-badge si-badge-info"><?php echo esc_html__('Step 3', 'simple-invoice'); ?></span>
                    </div>
                </div>

                <div class="si-summary-container">
                    <div class="si-summary-left">
                        <div class="si-summary-calculations">
                            <div class="si-calc-row">
                                <label class="si-calc-label">
                                    <span class="dashicons dashicons-money-alt"></span>
                                    <?php echo esc_html__('Subtotal', 'simple-invoice'); ?>
                                </label>
                                <span class="si-calc-value" id="si-subtotal">$0.00</span>
                            </div>

                            <div class="si-calc-row si-calc-input-row">
                                <label class="si-calc-label">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php echo esc_html__('Tax Rate (%)', 'simple-invoice'); ?>
                                </label>
                                <div class="si-calc-input-wrapper">
                                    <input type="number" class="si-calc-input" id="si-tax-rate" name="tax_rate" step="0.01" min="0" value="0" placeholder="0.00" />
                                    <span class="si-input-suffix">%</span>
                                </div>
                            </div>

                            <div class="si-calc-row">
                                <label class="si-calc-label">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <?php echo esc_html__('Tax Amount', 'simple-invoice'); ?>
                                </label>
                                <span class="si-calc-value" id="si-tax-amount">$0.00</span>
                            </div>

                            <div class="si-calc-row si-calc-input-row">
                                <label class="si-calc-label">
                                    <span class="dashicons dashicons-tickets-alt"></span>
                                    <?php echo esc_html__('Discount', 'simple-invoice'); ?>
                                </label>
                                <div class="si-calc-input-wrapper">
                                    <span class="si-input-prefix">$</span>
                                    <input type="number" class="si-calc-input" id="si-discount" name="discount" step="0.01" min="0" value="0" placeholder="0.00" />
                                </div>
                            </div>

                            <div class="si-calc-row si-calc-input-row">
                                <label class="si-calc-label">
                                    <span class="dashicons dashicons-car"></span>
                                    <?php echo esc_html__('Shipping', 'simple-invoice'); ?>
                                </label>
                                <div class="si-calc-input-wrapper">
                                    <span class="si-input-prefix">$</span>
                                    <input type="number" class="si-calc-input" id="si-shipping" name="shipping" step="0.01" min="0" value="0" placeholder="0.00" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="si-summary-right">
                        <div class="si-total-card si-enhanced-total">
                            <div class="si-total-header">
                                <div class="si-total-icon">
                                    <span class="dashicons dashicons-money-alt"></span>
                                </div>
                                <h3><?php echo esc_html__('Total Amount', 'simple-invoice'); ?></h3>
                            </div>
                            <div class="si-total-amount-display">
                                <span class="si-currency"><?php
                                    $settings = si_get_settings();
                                    echo esc_html($settings['currency_symbol'] ?? '$');
                                ?></span>
                                <span class="si-amount" id="si-total-amount">0.00</span>
                            </div>
                            <div class="si-total-footer">
                                <span class="si-total-label"><?php echo esc_html__('Invoice Total', 'simple-invoice'); ?></span>
                                <div class="si-total-status" id="si-total-status">
                                    <span class="si-status-indicator"></span>
                                    <span class="si-status-text"><?php echo esc_html__('Ready to create', 'simple-invoice'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Notes Section -->
            <div class="si-form-section si-notes-section si-step-content" data-step="4">
                <div class="si-section-header">
                    <div class="si-section-icon">
                        <span class="dashicons dashicons-edit-page"></span>
                    </div>
                    <div class="si-section-content">
                        <h2><?php echo esc_html__('Additional Notes', 'simple-invoice'); ?></h2>
                        <p class="si-section-description"><?php echo esc_html__('Add any special instructions or notes for your client', 'simple-invoice'); ?></p>
                    </div>
                    <div class="si-section-badge">
                        <span class="si-badge si-badge-success"><?php echo esc_html__('Step 4', 'simple-invoice'); ?></span>
                    </div>
                </div>

                <div class="si-notes-wrapper">
                    <textarea id="si-invoice-notes"
                              name="notes"
                              rows="5"
                              class="si-textarea"
                              placeholder="<?php echo esc_attr__('Enter any additional notes, payment terms, or special instructions for the client...', 'simple-invoice'); ?>"></textarea>
                    <div class="si-notes-help">
                        <span class="dashicons dashicons-info"></span>
                        <?php echo esc_html__('These notes will appear at the bottom of your invoice', 'simple-invoice'); ?>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="si-form-actions si-enhanced-actions">
                <div class="si-actions-container">
                    <div class="si-actions-center">
                        <button type="button" class="si-btn si-btn-primary si-btn-large si-btn-enhanced" id="si-save-invoice">
                            <span class="dashicons dashicons-saved"></span>
                            <?php echo esc_html__('Save Invoice', 'simple-invoice'); ?>
                            <span class="si-btn-shine"></span>
                        </button>
                    </div>
                </div>
            </div>
            
        </form>
        
    <?php endif; ?>
</div>



<!-- Quick Add Client Modal -->
<div id="si-quick-client-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        <div class="si-modal-body">
            <form id="si-quick-client-form" class="si-form">
                <div class="si-form-grid si-grid-2">
                    <div class="si-form-field">
                        <label for="si-quick-client-name" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Full Name', 'simple-invoice'); ?></span>
                            <span class="si-required">*</span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></span>
                            <input type="text" id="si-quick-client-name" name="name" class="si-form-input" required placeholder="<?php echo esc_attr__('Enter client full name', 'simple-invoice'); ?>" />
                        </div>
                    </div>
                    <div class="si-form-field">
                        <label for="si-quick-client-business" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 21h18"></path><path d="M5 21V7l8-4v18"></path><path d="M19 21V11l-6-4"></path></svg></span>
                            <input type="text" id="si-quick-client-business" name="business_name" class="si-form-input" placeholder="<?php echo esc_attr__('Business or company name', 'simple-invoice'); ?>" />
                        </div>
                    </div>
                    <div class="si-form-field">
                        <label for="si-quick-client-email" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Email Address', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg></span>
                            <input type="email" id="si-quick-client-email" name="email" class="si-form-input" placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                        </div>
                    </div>
                    <div class="si-form-field">
                        <label for="si-quick-client-phone" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Phone Number', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg></span>
                            <input type="text" id="si-quick-client-phone" name="phone" class="si-form-input" placeholder="<?php echo esc_attr__('+****************', 'simple-invoice'); ?>" />
                        </div>
                    </div>
                    <div class="si-form-field">
                        <label for="si-quick-client-gst" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('GST Number', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline></svg></span>
                            <input type="text" id="si-quick-client-gst" name="gst_number" class="si-form-input" placeholder="<?php echo esc_attr__('Enter GST number (e.g., 22AAAAA0000A1Z5)', 'simple-invoice'); ?>" />
                        </div>
                    </div>
                    <div class="si-form-field si-field-full">
                        <label for="si-quick-client-address" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Address', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg></span>
                            <textarea id="si-quick-client-address" name="address" rows="3" class="si-form-textarea" placeholder="<?php echo esc_attr__('Client address', 'simple-invoice'); ?>"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="si-modal-footer">
            <button type="button" class="si-btn si-btn-ghost si-modal-close"><?php echo esc_html__('Cancel', 'simple-invoice'); ?></button>
            <button type="button" class="si-btn si-btn-primary" id="si-save-quick-client"><?php echo esc_html__('Add Client', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<!-- Invoice Preview Modal -->
<div id="si-preview-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Invoice Preview', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        
        <div class="si-modal-body">
            <div id="si-preview-content">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
        
        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Close', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-create-from-preview">
                <?php echo esc_html__('Create & Download PDF', 'simple-invoice'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Define ajaxurl if not already defined
    if (typeof ajaxurl === 'undefined') {
        var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
    }

    var itemCounter = 0;

    // Initialize with default fields
    resetItemsTable();

    // Check if template is pre-selected and load its fields
    var preSelectedTemplate = $('#si-template-select').val();
    if (preSelectedTemplate) {
        $('#si-preview-layout').show();
        loadTemplateFields(preSelectedTemplate);
    }

    // Add invoice item
    $('#si-add-item, .si-add-first-item').on('click', function() {
        addInvoiceItem();
    });

    // Remove invoice item
    $(document).on('click', '.si-remove-item', function() {
        $(this).closest('tr').remove();
        updateSerialNumbers();
        calculateTotals();
        updateEmptyState();
    });

    // Calculate totals on input change
    $(document).on('input', '.si-item-quantity, .si-item-rate, .si-calc-input', function() {
        calculateTotals();
    });

    // Quick add client modal functionality - Use event delegation
    $(document).on('click', '.si-add-client-quick', function(e) {

        e.preventDefault();
        openQuickClientModal();
    });

    // Close modal functionality
    $('.si-modal-close').on('click', function() {
        closeQuickClientModal();
    });

    // Close modal when clicking backdrop
    $(document).on('click', '.si-modal', function(e) {
        if (e.target === this) {
            closeQuickClientModal();
        }
    });

    // Close modal with Escape key and handle Enter key
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27 && $('#si-quick-client-modal').is(':visible')) {
            closeQuickClientModal();
        }
    });

    // Handle Enter key in modal form
    $('#si-quick-client-form').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            $('#si-save-quick-client').click();
        }
    });

    // Save quick client
    $('#si-save-quick-client').on('click', function() {
        saveQuickClient();
    });

    // Preview layout
    $('#si-preview-layout').on('click', function() {
        var templateId = $('#si-template-select').val();
        if (templateId) {
            previewLayout(templateId);
        } else {
            alert('<?php echo esc_js(__('Please select a template first.', 'simple-invoice')); ?>');
        }
    });

    // Save invoice only
    $('#si-save-invoice').on('click', function() {
        if (validateForm()) {
            saveInvoice();
        }
    });

    // Close modals - handled by SI_Admin modal system

    // Template selection - show/hide eye button and load template fields
    $('#si-template-select').on('change', function() {
        var templateId = $(this).val();
        if (templateId) {
            $('#si-preview-layout').show();
            loadTemplateFields(templateId);
        } else {
            $('#si-preview-layout').hide();
            resetItemsTable();
        }
    });

    // Store current template fields
    var currentTemplateFields = [];

    function loadTemplateFields(templateId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_template_fields',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_get_template_fields_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data.body_fields && response.data.body_fields.length > 0) {
                    currentTemplateFields = response.data.body_fields;
                    updateItemsTableHeader();
                    // Clear existing items and add one new item with the new structure
                    $('#si-items-body').empty();
                    addInvoiceItem();
                } else {
                    // No fields configured - show empty table with message
                    currentTemplateFields = [];
                    showNoFieldsConfiguredMessage();
                }
            },
            error: function() {
                console.error('Error loading template fields');
                // Show error message instead of default fields
                currentTemplateFields = [];
                showNoFieldsConfiguredMessage();
            }
        });
    }

    function resetItemsTable() {
        // Reset to no fields - user must configure template first
        currentTemplateFields = [];
        showNoFieldsConfiguredMessage();
    }

    function showNoFieldsConfiguredMessage() {
        // Show message that no fields are configured
        var messageHtml = '<tr><th colspan="100%" style="text-align: center; padding: 20px; background: #f8f9fa; color: #6c757d;">';
        messageHtml += '<?php echo esc_js(__('No table columns configured for this Invoice Layout. Please edit the Invoice Layout to add table columns.', 'simple-invoice')); ?>';
        messageHtml += '</th></tr>';
        $('#si-items-head').html(messageHtml);

        var bodyMessageHtml = '<tr><td colspan="100%" style="text-align: center; padding: 40px; color: #6c757d; font-style: italic;">';
        bodyMessageHtml += '<?php echo esc_js(__('Configure table columns in the Invoice Layout to start adding items.', 'simple-invoice')); ?>';
        bodyMessageHtml += '</td></tr>';
        $('#si-items-body').html(bodyMessageHtml);

        // Hide add item button when no fields configured
        $('#si-add-item, .si-add-first-item').hide();
    }

    function updateItemsTableHeader() {
        if (currentTemplateFields.length === 0) {
            showNoFieldsConfiguredMessage();
            return;
        }

        var headerHtml = '<tr>';
        currentTemplateFields.forEach(function(field) {
            headerHtml += '<th>' + field.label + '</th>';
        });
        headerHtml += '<th><?php echo esc_html__('Action', 'simple-invoice'); ?></th>';
        headerHtml += '</tr>';
        $('#si-items-head').html(headerHtml);

        // Show add item button when fields are configured
        $('#si-add-item, .si-add-first-item').show();
    }

    function addInvoiceItem() {
        // Don't add items if no fields are configured
        if (currentTemplateFields.length === 0) {
            alert('<?php echo esc_js(__('Please configure table columns in the Invoice Layout before adding items.', 'simple-invoice')); ?>');
            return;
        }

        itemCounter++;
        var rowHtml = '<tr data-item-index="' + itemCounter + '">';

        // Use only the configured template fields
        var fieldsToUse = currentTemplateFields;

        fieldsToUse.forEach(function(field) {
            rowHtml += '<td>';

            switch(field.type) {
                case 'serial':
                    rowHtml += '<span class="si-item-serial">' + itemCounter + '</span>';
                    break;

                case 'text':
                    var placeholder = field.name === 'description' ? '<?php echo esc_attr__('Enter item description...', 'simple-invoice'); ?>' :
                                     'Enter ' + field.label.toLowerCase() + '...';
                    var className = 'si-item-' + field.name;
                    var required = field.required ? 'required' : '';
                    rowHtml += '<input type="text" class="' + className + '" name="items[' + itemCounter + '][' + field.name + ']" placeholder="' + placeholder + '" ' + required + ' />';
                    break;

                case 'number':
                    var className = 'si-item-' + field.name;
                    var required = field.required ? 'required' : '';
                    var defaultValue = field.name === 'quantity' ? '1' : '';
                    rowHtml += '<input type="number" class="' + className + '" name="items[' + itemCounter + '][' + field.name + ']" step="1" min="0" value="' + defaultValue + '" ' + required + ' />';
                    break;

                case 'currency':
                    var className = 'si-item-' + field.name;
                    var required = field.required ? 'required' : '';
                    rowHtml += '<input type="number" class="' + className + '" name="items[' + itemCounter + '][' + field.name + ']" step="0.01" min="0" placeholder="0.00" ' + required + ' />';
                    break;

                case 'calculated':
                    var className = 'si-item-' + field.name;
                    rowHtml += '<span class="' + className + '">$0.00</span>';
                    break;

                default:
                    // For custom field types, default to text input
                    var className = 'si-item-' + field.name;
                    var required = field.required ? 'required' : '';
                    rowHtml += '<input type="text" class="' + className + '" name="items[' + itemCounter + '][' + field.name + ']" placeholder="Enter ' + field.label.toLowerCase() + '..." ' + required + ' />';
                    break;
            }

            rowHtml += '</td>';
        });

        // Actions column
        rowHtml += '<td><button type="button" class="si-remove-item" title="<?php echo esc_attr__('Remove this item', 'simple-invoice'); ?>"><span class="dashicons dashicons-trash"></span></button></td>';

        rowHtml += '</tr>';

        $('#si-items-body').append(rowHtml);
        updateEmptyState();
        calculateTotals();

        // Focus on the first text input field of the new item
        setTimeout(function() {
            var firstInput = $('#si-items-body tr:last-child input[type="text"]:first');
            if (firstInput.length > 0) {
                firstInput.get(0).focus();
            }
        }, 100);
    }

    function updateSerialNumbers() {
        $('#si-items-body tr').each(function(index) {
            $(this).find('.si-item-serial').text(index + 1);
            $(this).attr('data-item-index', index + 1);
        });
        itemCounter = $('#si-items-body tr').length;
    }

    function updateEmptyState() {
        var itemCount = $('#si-items-body tr').length;
        var hasItems = itemCount > 0;

        // Update items count display
        var countText = itemCount + ' ' + (itemCount === 1 ? '<?php echo esc_js(__('item', 'simple-invoice')); ?>' : '<?php echo esc_js(__('items', 'simple-invoice')); ?>');
        $('#si-items-count').text(countText);

        if (hasItems) {
            $('#si-items-empty').hide();
            $('#si-invoice-items').show();
        } else {
            $('#si-items-empty').show();
            $('#si-invoice-items').hide();
        }
    }

    function validateForm() {
        var isValid = true;
        var errors = [];

        // Check if client is selected
        if (!$('#si-client-select').val()) {
            <?php if (empty($clients)): ?>
                errors.push('<?php echo esc_js(__('Please add a client first using the + button next to the client field.', 'simple-invoice')); ?>');
            <?php else: ?>
                errors.push('<?php echo esc_js(__('Please select a client.', 'simple-invoice')); ?>');
            <?php endif; ?>
            isValid = false;
        }

        // Check if template is selected
        if (!$('#si-template-select').val()) {
            errors.push('<?php echo esc_js(__('Please select a template.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Check if there are items
        if ($('#si-items-body tr').length === 0) {
            errors.push('<?php echo esc_js(__('Please add at least one item.', 'simple-invoice')); ?>');
            isValid = false;
        }

        // Validate item fields
        var hasEmptyItems = false;
        $('#si-items-body tr').each(function() {
            var description = $(this).find('.si-item-description').val().trim();
            var quantity = $(this).find('.si-item-quantity').val();
            var rate = $(this).find('.si-item-rate').val();

            if (!description || !quantity || !rate) {
                hasEmptyItems = true;
            }
        });

        if (hasEmptyItems) {
            errors.push('<?php echo esc_js(__('Please fill in all item fields.', 'simple-invoice')); ?>');
            isValid = false;
        }

        if (!isValid) {
            alert(errors.join('\\n'));
        }

        return isValid;
    }
    
    function calculateTotals() {
        var subtotal = 0;

        // Get currency symbol from localized data
        var currencySymbol = (typeof wimp_admin !== 'undefined' && wimp_admin.currency_symbol)
            ? wimp_admin.currency_symbol
            : '$';

        // Calculate item totals
        $('#si-items-body tr').each(function() {
            var row = $(this);
            var quantity = parseFloat(row.find('.si-item-quantity').val()) || 0;
            var rate = parseFloat(row.find('.si-item-rate').val()) || 0;
            var total = quantity * rate;

            row.find('.si-item-total').text(currencySymbol + total.toFixed(2));
            subtotal += total;
        });

        // Update subtotal
        $('#si-subtotal').text(currencySymbol + subtotal.toFixed(2));

        // Get tax, discount, and shipping values
        var taxRate = parseFloat($('#si-tax-rate').val()) || 0;
        var discount = parseFloat($('#si-discount').val()) || 0;
        var shipping = parseFloat($('#si-shipping').val()) || 0;

        // Calculate tax amount
        var taxAmount = subtotal * (taxRate / 100);
        $('#si-tax-amount').text(currencySymbol + taxAmount.toFixed(2));

        // Calculate final total
        var finalTotal = subtotal + taxAmount - discount + shipping;
        $('#si-total-amount').text(finalTotal.toFixed(2));

        // Update currency symbol in total card
        $('.si-currency').text(currencySymbol);

        // Update total card styling based on amount
        var totalCard = $('.si-total-card');
        if (finalTotal > 0) {
            totalCard.removeClass('si-total-zero');
        } else {
            totalCard.addClass('si-total-zero');
        }
    }
    



    
    function previewInvoice() {
        var formData = collectInvoiceData();

        // Show loading state
        $('#si-preview-invoice').prop('disabled', true).text('<?php echo esc_js(__('Loading...', 'simple-invoice')); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_preview_invoice',
                client_id: formData.client_id,
                template_id: formData.template_id,
                invoice_data: formData.invoice_data,
                nonce: '<?php echo wp_create_nonce('si_preview_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('#si-preview-content').html(response.data.html_content);
                    $('#si-preview-modal').show();
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to generate preview.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                alert('<?php echo esc_js(__('An error occurred while generating preview.', 'simple-invoice')); ?>');
            },
            complete: function() {
                // Reset button state
                $('#si-preview-invoice').prop('disabled', false).text('<?php echo esc_js(__('Preview Invoice', 'simple-invoice')); ?>');
            }
        });
    }
    
    function createInvoice() {
        var formData = collectInvoiceData();

        // Show loading state
        $('#si-create-invoice, #si-create-from-preview').prop('disabled', true).text('<?php echo esc_js(__('Creating...', 'simple-invoice')); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_create_invoice',
                client_id: formData.client_id,
                template_id: formData.template_id,
                invoice_number: formData.invoice_number,
                invoice_data: formData.invoice_data,
                nonce: '<?php echo wp_create_nonce('si_create_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Success - no alert as per user preference

                    // Open download URL in new window/tab
                    window.open(response.data.download_url, '_blank');

                    // Reset form after successful creation
                    $('#si-invoice-form')[0].reset();
                    $('#si-items-body').empty();
                    addInvoiceItem(); // Add one default item
                    calculateTotals();
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to create invoice.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            },
            complete: function() {
                // Reset button state
                $('#si-create-invoice, #si-create-from-preview').prop('disabled', false).text('<?php echo esc_js(__('Create & Download PDF', 'simple-invoice')); ?>');
            }
        });
    }
    


    // Progress indicator functionality
    function updateProgressIndicator() {
        var currentStep = 1;
        var totalSteps = 4;

        // Determine current step based on form completion
        if ($('#si-template-select').val() && $('#si-client-select').val()) {
            currentStep = 2;
        }

        if ($('#si-items-body tr').length > 0) {
            currentStep = 3;
        }

        if (parseFloat($('#si-total-amount').text()) > 0) {
            currentStep = 4;
        }

        // Update step indicators
        $('.si-step').removeClass('si-step-active');
        for (var i = 1; i <= currentStep; i++) {
            $('.si-step[data-step="' + i + '"]').addClass('si-step-active');
        }

        // Update progress bar
        var progressPercent = (currentStep / totalSteps) * 100;
        $('.si-progress-fill').css('width', progressPercent + '%');
    }

    // Smooth scroll to section
    function scrollToSection(step) {
        var target = $('.si-step-content[data-step="' + step + '"]');
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    }

    // Click handler for progress steps
    $('.si-step').on('click', function() {
        var step = $(this).data('step');
        scrollToSection(step);
    });



    // Update progress on form changes
    $(document).on('change input', '#si-template-select, #si-client-select, .si-item-description, .si-item-quantity, .si-item-rate, .si-calc-input', function() {
        setTimeout(updateProgressIndicator, 100);
    });

    // Initialize currency symbol on page load
    function initializeCurrencySymbol() {
        var currencySymbol = (typeof wimp_admin !== 'undefined' && wimp_admin.currency_symbol)
            ? wimp_admin.currency_symbol
            : '$';
        $('.si-currency').text(currencySymbol);
    }

    // Listen for currency updates from settings page
    $(document).on('currency_updated', function(event, newSymbol) {
        $('.si-currency').text(newSymbol);
        // Recalculate totals to update all currency displays
        calculateTotals();
    });

    // Initialize page
    $(document).ready(function() {


        // Check if elements exist
        var buttonCount = $('.si-add-client-quick').length;
        var modalCount = $('#si-quick-client-modal').length;

        // Initialize currency symbol
        initializeCurrencySymbol();

        updateEmptyState();
        calculateTotals();
        updateProgressIndicator();

        // Add smooth animations to form sections
        $('.si-form-section').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            }).delay(index * 100).animate({
                'opacity': '1',
                'transform': 'translateY(0)'
            }, 400);
        });
    });
});
</script>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>

<!-- Immediate Footer Hide Script -->
<script type="text/javascript">
// Global functions
function collectInvoiceData() {
    var items = [];

    // Collect items
    jQuery('#si-items-body tr').each(function() {
        var row = jQuery(this);
        var item = {
            description: row.find('.si-item-description').val(),
            quantity: parseFloat(row.find('.si-item-quantity').val()) || 0,
            rate: parseFloat(row.find('.si-item-rate').val()) || 0
        };

        if (item.description && item.quantity > 0 && item.rate >= 0) {
            items.push(item);
        }
    });

    var formData = {
        client_id: jQuery('#si-client-select').val(),
        template_id: jQuery('#si-template-select').val(),
        invoice_number: jQuery('#si-invoice-number').val(),
        invoice_data: {
            items: items,
            tax_rate: parseFloat(jQuery('#si-tax-rate').val()) || 0,
            discount: parseFloat(jQuery('#si-discount').val()) || 0,
            shipping: parseFloat(jQuery('#si-shipping').val()) || 0,
            notes: jQuery('#si-invoice-notes').val()
        }
    };

    return formData;
}

function openQuickClientModal() {
    var modal = jQuery('#si-quick-client-modal');

    if (modal.length === 0) {
        return; // Modal not found
        return;
    }

    // Enhanced modal opening - multiple approaches for maximum compatibility


    // Method 1: Remove any conflicting styles
    modal.removeAttr('style');

    // Method 2: Force display with inline styles (highest priority)
    modal.css({
        'display': 'flex',
        'opacity': '1',
        'visibility': 'visible',
        'z-index': '2147483647'
    });

    // Method 3: Add show classes
    modal.addClass('show si-modal-show');

    // Add body class to prevent scrolling
    jQuery('body').addClass('si-modal-open');



    // Focus first input
    setTimeout(function() {
        var firstInput = jQuery('#si-quick-client-name');
        if (firstInput.length) {
            firstInput[0].focus();
        }
    }, 100);
}

function closeQuickClientModal() {
    var modal = jQuery('#si-quick-client-modal');

    // Enhanced modal closing - multiple approaches for maximum compatibility


    // Method 1: Remove show classes
    modal.removeClass('show si-modal-show');

    // Method 2: Force hide with inline styles
    modal.css({
        'display': 'none',
        'opacity': '0',
        'visibility': 'hidden'
    });

    // Remove body class to restore scrolling
    jQuery('body').removeClass('si-modal-open');

    if (jQuery('#si-quick-client-form')[0]) {
        jQuery('#si-quick-client-form')[0].reset();
    }

    // Clear any validation states
    jQuery('.si-form-input, .si-form-textarea').removeClass('si-error si-success');


}

function saveQuickClient() {
    // Validate required fields
    var clientName = jQuery('#si-quick-client-name').val().trim();
    if (!clientName) {
        alert('<?php echo esc_js(__('Please enter client name.', 'simple-invoice')); ?>');
        var nameInput = jQuery('#si-quick-client-name');
        if (nameInput.length) {
            nameInput[0].focus();
        }
        return;
    }

    var formData = jQuery('#si-quick-client-form').serialize();
    formData += '&action=si_add_client&nonce=<?php echo wp_create_nonce('si_add_client_nonce'); ?>';

    // Show loading state
    var saveBtn = jQuery('#si-save-quick-client');
    var originalText = saveBtn.text();
    saveBtn.prop('disabled', true).text('<?php echo esc_js(__('Saving...', 'simple-invoice')); ?>');

    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: formData,
        success: function(response) {
            if (response.success && response.data.client) {
                var client = response.data.client;
                var optionText = client.name;
                if (client.business_name) {
                    optionText += ' (' + client.business_name + ')';
                }

                // Check if this is the first client being added
                var clientSelect = jQuery('#si-client-select');
                var firstOption = clientSelect.find('option:first');

                // If the first option indicates no clients available, replace it
                if (firstOption.text().indexOf('No clients available') !== -1) {
                    clientSelect.empty();
                    clientSelect.append('<option value=""><?php echo esc_js(__('Choose a client...', 'simple-invoice')); ?></option>');
                }

                clientSelect.append('<option value="' + client.id + '">' + optionText + '</option>');
                clientSelect.val(client.id);

                // Show success and close modal
                saveBtn.text('<?php echo esc_js(__('Saved!', 'simple-invoice')); ?>');
                setTimeout(function() {
                    closeQuickClientModal();
                    // Update progress after adding client
                    if (typeof updateProgressIndicator === 'function') {
                        updateProgressIndicator();
                    }
                }, 1000);
            } else {
                saveBtn.prop('disabled', false).text(originalText);
                alert(response.message || '<?php echo esc_js(__('Failed to add client.', 'simple-invoice')); ?>');
            }
        },
        error: function() {
            saveBtn.prop('disabled', false).text(originalText);
            alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
        }
    });
}

function previewLayout(templateId) {
    // Get selected template info
    var selectedOption = jQuery('#si-template-select option:selected');
    var templateName = selectedOption.text();
    var designName = selectedOption.data('design') || 'classic';

    // Create modal with template info
    var previewModal = jQuery('<div class="si-modal si-modal-large" style="display: block;"><div class="si-modal-content"><div class="si-modal-header"><h2><?php echo esc_js(__('Layout Preview', 'simple-invoice')); ?></h2><button type="button" class="si-modal-close">&times;</button></div><div class="si-modal-body"><div class="si-layout-preview"><div class="si-template-info"><h3>' + templateName + '</h3><p class="si-design-type">' + designName.charAt(0).toUpperCase() + designName.slice(1) + ' Design</p></div><div class="si-preview-placeholder si-design-' + designName + '"><div class="si-preview-mockup"><div class="si-mockup-header"><div class="si-mockup-logo"></div><div class="si-mockup-title">INVOICE</div></div><div class="si-mockup-content"><div class="si-mockup-client"><div class="si-mockup-line"></div><div class="si-mockup-line short"></div></div><div class="si-mockup-items"><div class="si-mockup-row header"></div><div class="si-mockup-row"></div><div class="si-mockup-row"></div></div><div class="si-mockup-total"><div class="si-mockup-line"></div></div></div></div><p class="si-preview-note"><?php echo esc_js(__('This is a preview of how your invoice will look with the selected template', 'simple-invoice')); ?></p></div></div></div></div></div>');

    jQuery('body').append(previewModal);

    // Close modal functionality
    previewModal.find('.si-modal-close').on('click', function() {
        previewModal.remove();
    });

    previewModal.on('click', function(e) {
        if (e.target === this) {
            previewModal.remove();
        }
    });
}

function saveInvoice() {
    var formData = collectInvoiceData();



    // Show loading state
    jQuery('#si-save-invoice').prop('disabled', true).text('<?php echo esc_js(__('Saving...', 'simple-invoice')); ?>');

    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'si_save_invoice',
            client_id: formData.client_id,
            template_id: formData.template_id,
            invoice_number: formData.invoice_number,
            invoice_data: formData.invoice_data,
            nonce: '<?php echo wp_create_nonce('si_save_invoice_nonce'); ?>'
        },
        success: function(response) {

            jQuery('#si-save-invoice').prop('disabled', false).text('<?php echo esc_js(__('Save Invoice', 'simple-invoice')); ?>');

            if (response.success) {
                // Redirect to All Invoices page without alert
                window.location.href = '<?php echo admin_url('admin.php?page=wimp-invoices'); ?>';
            } else {
                console.error('Save failed:', response);
                alert(response.data || '<?php echo esc_js(__('Failed to save invoice.', 'simple-invoice')); ?>');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr, status, error);
            jQuery('#si-save-invoice').prop('disabled', false).text('<?php echo esc_js(__('Save Invoice', 'simple-invoice')); ?>');
            alert('<?php echo esc_js(__('An error occurred while saving.', 'simple-invoice')); ?>');
        }
    });
}



jQuery(document).ready(function($) {
    // Hide WordPress footer immediately
    $('#wpfooter').hide();

    // Also hide it with CSS for extra safety
    $('<style type="text/css">#wpfooter { display: none !important; }</style>').appendTo('head');

    // Ensure proper spacing
    $('#wpbody-content').css({
        'padding-bottom': '60px',
        'min-height': 'calc(100vh - 160px)'
    });
});
</script>

<style type="text/css">
/* Immediate CSS fix with highest specificity */
#wpfooter {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

#wpbody-content {
    padding-bottom: 60px !important;
    min-height: calc(100vh - 160px) !important;
}

/* Ensure our plugin footer stays at bottom */
.si-page-container {
    min-height: calc(100vh - 200px);
    position: relative;
}
</style>
