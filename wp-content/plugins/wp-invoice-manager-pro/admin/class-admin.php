<?php
/**
 * Admin Interface Class
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SI_Admin class for managing admin interface
 *
 * @since 1.0.0
 */
class SI_Admin {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->si_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function si_init_hooks() {
        add_action('admin_menu', array($this, 'si_add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'si_enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'si_admin_init'));
        add_action('wp_ajax_si_save_settings', array($this, 'si_ajax_save_settings'));
        add_action('wp_ajax_si_clear_data', array($this, 'si_ajax_clear_data'));
        add_action('wp_ajax_si_clear_selected_data', array($this, 'si_ajax_clear_selected_data'));
        add_action('wp_ajax_si_reset_settings', array($this, 'si_ajax_reset_settings'));
        add_action('wp_ajax_si_get_template', array($this, 'si_ajax_get_template'));
        add_action('wp_ajax_si_add_template', array($this, 'si_ajax_add_template'));
        add_action('wp_ajax_si_edit_template', array($this, 'si_ajax_edit_template'));
        add_action('wp_ajax_si_delete_template', array($this, 'si_ajax_delete_template'));
        add_action('wp_ajax_si_preview_design', array($this, 'si_ajax_preview_design'));
        add_action('wp_ajax_si_get_template_fields', array($this, 'si_ajax_get_template_fields'));

        // Client AJAX handlers are handled by SI_Client class
    }

    /**
     * Add admin menu
     *
     * @since 1.0.0
     */
    public function si_add_admin_menu() {
        // Main menu
        add_menu_page(
            __('WordPress Invoice Manager Pro', 'wp-invoice-manager-pro'),
            __('Invoice Manager Pro', 'wp-invoice-manager-pro'),
            'read',
            'wp-invoice-manager-pro',
            array($this, 'si_dashboard_page'),
            'dashicons-media-text',
            26  // Position after Tools (25) and before Settings (80)
        );

        // Dashboard submenu (rename the first submenu)
        add_submenu_page(
            'wp-invoice-manager-pro',
            __('Dashboard', 'wp-invoice-manager-pro'),
            '<span class="dashicons dashicons-dashboard" style="font-size: 16px; width: 16px; height: 16px; margin-right: 5px;"></span>' . __('Dashboard', 'wp-invoice-manager-pro'),
            'read',
            'wp-invoice-manager-pro',
            array($this, 'si_dashboard_page')
        );

        // All Invoices submenu (view existing invoices)
        add_submenu_page(
            'wp-invoice-manager-pro',
            __('All Invoices', 'wp-invoice-manager-pro'),
            '<span class="dashicons dashicons-list-view" style="font-size: 16px; width: 16px; height: 16px; margin-right: 5px;"></span>' . __('All Invoices', 'wp-invoice-manager-pro'),
            'read',
            'wimp-invoices',
            array($this, 'si_invoices_page')
        );

        // Create Invoice as submenu under All Invoices (WordPress standard way)
        add_submenu_page(
            'wimp-invoices',
            __('Create Invoice', 'wp-invoice-manager-pro'),
            __('Create Invoice', 'wp-invoice-manager-pro'),
            'read',
            'wimp-create-invoice',
            array($this, 'si_create_invoice_page')
        );

        // Clients submenu (manage clients)
        add_submenu_page(
            'wp-invoice-manager-pro',
            __('Clients', 'wp-invoice-manager-pro'),
            '<span class="dashicons dashicons-groups" style="font-size: 16px; width: 16px; height: 16px; margin-right: 5px;"></span>' . __('Clients', 'wp-invoice-manager-pro'),
            'read',
            'wimp-clients',
            array($this, 'si_clients_page')
        );

        // Invoice Type submenu (manage invoice types)
        add_submenu_page(
            'wp-invoice-manager-pro',
            __('Invoice Type', 'wp-invoice-manager-pro'),
            '<span class="dashicons dashicons-admin-page" style="font-size: 16px; width: 16px; height: 16px; margin-right: 5px;"></span>' . __('Invoice Type', 'wp-invoice-manager-pro'),
            'read',
            'wimp-templates',
            array($this, 'si_templates_page')
        );

        // Designs submenu
        add_submenu_page(
            'wp-invoice-manager-pro',
            __('Designs', 'wp-invoice-manager-pro'),
            '<span class="dashicons dashicons-admin-appearance" style="font-size: 16px; width: 16px; height: 16px; margin-right: 5px;"></span>' . __('Designs', 'wp-invoice-manager-pro'),
            'read',
            'wimp-designs',
            array($this, 'si_designs_page')
        );

        // Settings submenu (last item)
        add_submenu_page(
            'wp-invoice-manager-pro',
            __('Settings', 'wp-invoice-manager-pro'),
            '<span class="dashicons dashicons-admin-settings" style="font-size: 16px; width: 16px; height: 16px; margin-right: 5px;"></span>' . __('Settings', 'wp-invoice-manager-pro'),
            'read',
            'wimp-settings',
            array($this, 'si_settings_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page hook
     * @since 1.0.0
     */
    public function si_enqueue_admin_scripts($hook) {
        // Only load on WordPress Invoice Manager Pro pages
        if (strpos($hook, 'wp-invoice-manager-pro') === false && strpos($hook, 'wimp-') === false) {
            return;
        }

        // Enqueue WordPress media library
        wp_enqueue_media();

        // Enqueue admin styles
        wp_enqueue_style(
            'wimp-admin-style',
            WIMP_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            WIMP_PLUGIN_VERSION
        );

        // Enqueue admin scripts
        wp_enqueue_script(
            'wimp-admin-script',
            WIMP_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'wp-util'),
            WIMP_PLUGIN_VERSION,
            true
        );

        // Get currency settings for JavaScript
        $settings = si_get_settings();
        $currency_symbol = $settings['currency_symbol'] ?? '$';
        $currency_code = $settings['currency'] ?? 'USD';

        // Localize script
        wp_localize_script('wimp-admin-script', 'wimp_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wimp_admin_nonce'),
            'currency_symbol' => $currency_symbol,
            'currency_code' => $currency_code,
            'strings' => array(
                'confirm_delete' => __('Are you sure you want to delete this item?', 'wp-invoice-manager-pro'),
                'error_occurred' => __('An error occurred. Please try again.', 'wp-invoice-manager-pro'),
                'success' => __('Operation completed successfully.', 'wp-invoice-manager-pro'),
                'loading' => __('Loading...', 'wp-invoice-manager-pro')
            )
        ));
    }

    /**
     * Admin initialization
     *
     * @since 1.0.0
     */
    public function si_admin_init() {
        // Register settings with lower capability requirement
        register_setting('wimp_settings_group', 'wimp_settings', array(
            'sanitize_callback' => array($this, 'si_sanitize_settings'),
            'capability' => 'read'
        ));
    }

    /**
     * Dashboard page
     *
     * @since 1.0.0
     */
    public function si_dashboard_page() {
        include WIMP_PLUGIN_PATH . 'admin/views/dashboard-page.php';
    }

    /**
     * Settings page
     *
     * @since 1.0.0
     */
    public function si_settings_page() {
        include WIMP_PLUGIN_PATH . 'admin/views/settings-page.php';
    }

    /**
     * Clients page
     *
     * @since 1.0.0
     */
    public function si_clients_page() {
        include WIMP_PLUGIN_PATH . 'admin/views/clients-page.php';
    }

    /**
     * Templates page
     *
     * @since 1.0.0
     */
    public function si_templates_page() {
        // Get available designs
        $design_loader = new SI_Design_Loader();
        $available_designs = $design_loader->si_get_available_designs();

        // Get templates
        $template_manager = new SI_Template();
        $templates = $template_manager->si_get_templates();

        include WIMP_PLUGIN_PATH . 'admin/views/templates-page.php';
    }

    /**
     * Designs page
     *
     * @since 1.0.0
     */
    public function si_designs_page() {
        // Get available designs
        $design_loader = new SI_Design_Loader();
        $available_designs = $design_loader->si_get_available_designs();

        include WIMP_PLUGIN_PATH . 'admin/views/designs-page.php';
    }

    /**
     * Invoices page
     *
     * @since 1.0.0
     */
    public function si_invoices_page() {
        include WIMP_PLUGIN_PATH . 'admin/views/invoices-page.php';
    }

    /**
     * Create Invoice page
     *
     * @since 1.0.0
     */
    public function si_create_invoice_page() {
        include WIMP_PLUGIN_PATH . 'admin/views/create-invoice-page.php';
    }



    /**
     * Sanitize settings
     *
     * @param array $input Raw settings input
     * @return array Sanitized settings
     * @since 1.0.0
     */
    public function si_sanitize_settings($input) {
        $sanitized = array();

        // Business information
        $sanitized['business_name'] = si_sanitize_text($input['business_name'] ?? '');
        $sanitized['business_address'] = si_sanitize_textarea($input['business_address'] ?? '');
        $sanitized['business_email'] = si_sanitize_email($input['business_email'] ?? '');
        $sanitized['business_phone'] = si_sanitize_text($input['business_phone'] ?? '');
        $sanitized['business_logo'] = si_sanitize_url($input['business_logo'] ?? '');
        $sanitized['gstin'] = si_sanitize_text($input['gstin'] ?? '');

        // Invoice settings
        $sanitized['default_due_days'] = intval($input['default_due_days'] ?? 7);

        // Currency settings
        $sanitized['currency'] = si_sanitize_text($input['currency'] ?? 'USD');
        $sanitized['currency_symbol'] = si_sanitize_text($input['currency_symbol'] ?? '$');

        // Payment settings
        $sanitized['payment_methods'] = isset($input['payment_methods']) && is_array($input['payment_methods'])
            ? array_map('si_sanitize_text', $input['payment_methods'])
            : array();
        $sanitized['bank_details'] = si_sanitize_textarea($input['bank_details'] ?? '');
        $sanitized['paypal_email'] = si_sanitize_email($input['paypal_email'] ?? '');
        $sanitized['upi_id'] = si_sanitize_text($input['upi_id'] ?? '');

        // Footer settings
        $sanitized['footer_notes'] = si_sanitize_textarea($input['footer_notes'] ?? '');
        $sanitized['terms_text'] = si_sanitize_textarea($input['terms_text'] ?? '');

        // Data management settings
        $sanitized['clear_data_on_deactivation'] = isset($input['clear_data_on_deactivation']) ? 1 : 0;

        return $sanitized;
    }

    /**
     * AJAX handler for saving settings
     *
     * @since 1.0.0
     */
    public function si_ajax_save_settings() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_save_settings_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            si_send_json_response(false, __('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        // Sanitize and save settings
        $sanitized_settings = $this->si_sanitize_settings($_POST);
        $success = si_update_settings($sanitized_settings);

        if ($success) {
            si_send_json_response(true, __('Settings saved successfully.', 'simple-invoice'));
        } else {
            si_send_json_response(false, __('Failed to save settings.', 'simple-invoice'));
        }
    }

    /**
     * Get admin page URL
     *
     * @param string $page Page slug
     * @param array $args Additional arguments
     * @return string Admin page URL
     * @since 1.0.0
     */
    public function si_get_admin_url($page, $args = array()) {
        $base_args = array('page' => $page);
        $args = wp_parse_args($args, $base_args);
        
        return add_query_arg($args, admin_url('admin.php'));
    }

    /**
     * Display admin notice
     *
     * @param string $message Notice message
     * @param string $type Notice type (success, error, warning, info)
     * @since 1.0.0
     */
    public function si_display_admin_notice($message, $type = 'success') {
        $class = 'notice notice-' . $type;
        printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($message));
    }

    /**
     * Get current admin page
     *
     * @return string Current page slug
     * @since 1.0.0
     */
    public function si_get_current_page() {
        return isset($_GET['page']) ? sanitize_text_field($_GET['page']) : '';
    }

    /**
     * Check if current page is WordPress Invoice Manager Pro page
     *
     * @return bool True if on WordPress Invoice Manager Pro page
     * @since 1.0.0
     */
    public function si_is_plugin_page() {
        $current_page = $this->si_get_current_page();
        return strpos($current_page, 'wp-invoice-manager-pro') === 0 || strpos($current_page, 'wimp-') === 0;
    }

    /**
     * Render form field
     *
     * @param array $field Field configuration
     * @param mixed $value Current value
     * @since 1.0.0
     */
    public function si_render_field($field, $value = '') {
        $field = wp_parse_args($field, array(
            'type' => 'text',
            'name' => '',
            'id' => '',
            'class' => '',
            'placeholder' => '',
            'description' => '',
            'options' => array(),
            'attributes' => array()
        ));

        $attributes = '';
        foreach ($field['attributes'] as $attr => $attr_value) {
            $attributes .= sprintf(' %s="%s"', esc_attr($attr), esc_attr($attr_value));
        }

        echo '<div class="si-field-wrapper">';

        switch ($field['type']) {
            case 'text':
            case 'email':
            case 'url':
            case 'number':
                printf(
                    '<input type="%s" name="%s" id="%s" class="%s" value="%s" placeholder="%s"%s />',
                    esc_attr($field['type']),
                    esc_attr($field['name']),
                    esc_attr($field['id']),
                    esc_attr($field['class']),
                    esc_attr($value),
                    esc_attr($field['placeholder']),
                    $attributes
                );
                break;

            case 'textarea':
                printf(
                    '<textarea name="%s" id="%s" class="%s" placeholder="%s"%s>%s</textarea>',
                    esc_attr($field['name']),
                    esc_attr($field['id']),
                    esc_attr($field['class']),
                    esc_attr($field['placeholder']),
                    $attributes,
                    esc_textarea($value)
                );
                break;

            case 'select':
                printf('<select name="%s" id="%s" class="%s"%s>', 
                    esc_attr($field['name']),
                    esc_attr($field['id']),
                    esc_attr($field['class']),
                    $attributes
                );
                
                foreach ($field['options'] as $option_value => $option_label) {
                    printf(
                        '<option value="%s"%s>%s</option>',
                        esc_attr($option_value),
                        selected($value, $option_value, false),
                        esc_html($option_label)
                    );
                }
                
                echo '</select>';
                break;

            case 'checkbox':
                printf(
                    '<input type="checkbox" name="%s" id="%s" class="%s" value="1"%s%s />',
                    esc_attr($field['name']),
                    esc_attr($field['id']),
                    esc_attr($field['class']),
                    checked($value, 1, false),
                    $attributes
                );
                break;

            case 'media':
                printf(
                    '<input type="url" name="%s" id="%s" class="%s si-media-input" value="%s" placeholder="%s"%s />',
                    esc_attr($field['name']),
                    esc_attr($field['id']),
                    esc_attr($field['class']),
                    esc_attr($value),
                    esc_attr($field['placeholder']),
                    $attributes
                );
                printf(
                    '<button type="button" class="button si-media-button" data-target="%s">%s</button>',
                    esc_attr($field['id']),
                    __('Select Image', 'simple-invoice')
                );
                break;
        }

        if (!empty($field['description'])) {
            printf('<p class="description">%s</p>', esc_html($field['description']));
        }

        echo '</div>';
    }

    /**
     * Render table with pagination
     *
     * @param array $args Table configuration
     * @since 1.0.0
     */
    public function si_render_table($args) {
        $args = wp_parse_args($args, array(
            'columns' => array(),
            'data' => array(),
            'actions' => array(),
            'pagination' => false,
            'search' => false,
            'per_page' => 20,
            'current_page' => 1,
            'total_items' => 0
        ));

        echo '<div class="si-table-wrapper">';

        // Search form
        if ($args['search']) {
            echo '<div class="si-search-form">';
            echo '<input type="search" placeholder="' . esc_attr__('Search...', 'simple-invoice') . '" class="si-search-input" />';
            echo '<button type="button" class="button si-search-button">' . __('Search', 'simple-invoice') . '</button>';
            echo '</div>';
        }

        // Table
        echo '<table class="wp-list-table widefat fixed striped">';
        
        // Table header
        echo '<thead><tr>';
        foreach ($args['columns'] as $column_key => $column_label) {
            printf('<th scope="col">%s</th>', esc_html($column_label));
        }
        if (!empty($args['actions'])) {
            echo '<th scope="col">' . __('Actions', 'simple-invoice') . '</th>';
        }
        echo '</tr></thead>';

        // Table body
        echo '<tbody>';
        if (!empty($args['data'])) {
            foreach ($args['data'] as $row) {
                echo '<tr>';
                foreach ($args['columns'] as $column_key => $column_label) {
                    $value = isset($row[$column_key]) ? $row[$column_key] : '';
                    printf('<td>%s</td>', esc_html($value));
                }
                
                // Actions column
                if (!empty($args['actions'])) {
                    echo '<td class="si-actions-column">';
                    foreach ($args['actions'] as $action) {
                        $url = isset($action['url']) ? $action['url'] : '#';
                        $class = isset($action['class']) ? $action['class'] : 'button';
                        $data_attrs = '';
                        
                        if (isset($action['data'])) {
                            foreach ($action['data'] as $data_key => $data_value) {
                                $data_attrs .= sprintf(' data-%s="%s"', esc_attr($data_key), esc_attr($data_value));
                            }
                        }
                        
                        printf(
                            '<a href="%s" class="%s"%s>%s</a> ',
                            esc_url($url),
                            esc_attr($class),
                            $data_attrs,
                            esc_html($action['label'])
                        );
                    }
                    echo '</td>';
                }
                echo '</tr>';
            }
        } else {
            $colspan = count($args['columns']) + (!empty($args['actions']) ? 1 : 0);
            printf(
                '<tr><td colspan="%d" class="si-no-items">%s</td></tr>',
                $colspan,
                __('No items found.', 'simple-invoice')
            );
        }
        echo '</tbody>';
        
        echo '</table>';

        // Pagination
        if ($args['pagination'] && $args['total_items'] > $args['per_page']) {
            $total_pages = ceil($args['total_items'] / $args['per_page']);
            
            echo '<div class="si-pagination">';
            echo paginate_links(array(
                'base' => add_query_arg('paged', '%#%'),
                'format' => '',
                'prev_text' => __('&laquo; Previous', 'simple-invoice'),
                'next_text' => __('Next &raquo;', 'simple-invoice'),
                'current' => $args['current_page'],
                'total' => $total_pages
            ));
            echo '</div>';
        }

        echo '</div>';
    }

    /**
     * AJAX handler for clearing plugin data
     *
     * @since 1.0.0
     */
    public function si_ajax_clear_data() {


        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'] ?? '', 'si_clear_data_nonce')) {

            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {

            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        // Get clear options
        $clear_options = array();
        if (isset($_POST['clear_clients']) && $_POST['clear_clients']) {
            $clear_options[] = 'clients';
        }
        if (isset($_POST['clear_templates']) && $_POST['clear_templates']) {
            $clear_options[] = 'templates';
        }
        if (isset($_POST['clear_invoices']) && $_POST['clear_invoices']) {
            $clear_options[] = 'invoices';
        }
        if (isset($_POST['clear_settings']) && $_POST['clear_settings']) {
            $clear_options[] = 'settings';
        }



        if (empty($clear_options)) {
            wp_send_json_error(__('Please select at least one data type to clear.', 'simple-invoice'));
        }

        // Clear the selected data
        $result = si_clear_plugin_data($clear_options);



        if ($result) {
            $message = sprintf(
                __('Successfully cleared: %s', 'simple-invoice'),
                implode(', ', array_map('ucfirst', $clear_options))
            );
            wp_send_json_success($message);
        } else {
            wp_send_json_error(__('Failed to clear some data. Please try again.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for clearing selected data types
     *
     * @since 1.0.0
     */
    public function si_ajax_clear_selected_data() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'si_clear_data_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        // Get clear types
        $clear_types = $_POST['clear_types'] ?? array();
        if (empty($clear_types) || !is_array($clear_types)) {
            wp_send_json_error(__('Please select at least one data type to clear.', 'simple-invoice'));
        }

        // Sanitize clear types
        $allowed_types = array('invoices', 'clients', 'templates', 'custom_templates');
        $clear_types = array_intersect($clear_types, $allowed_types);

        if (empty($clear_types)) {
            wp_send_json_error(__('Invalid data types selected.', 'simple-invoice'));
        }

        // Handle custom templates separately
        if (in_array('custom_templates', $clear_types)) {
            $result = $this->si_clear_custom_templates();
            if (!$result) {
                wp_send_json_error(__('Failed to clear custom templates.', 'simple-invoice'));
            }
            // Remove custom_templates from the array as it's handled separately
            $clear_types = array_diff($clear_types, array('custom_templates'));
        }

        // Clear other data types if any remain
        if (!empty($clear_types)) {
            $result = si_clear_plugin_data($clear_types);
            if (!$result) {
                wp_send_json_error(__('Failed to clear selected data.', 'simple-invoice'));
            }
        }

        $cleared_items = array();
        foreach ($_POST['clear_types'] as $type) {
            switch ($type) {
                case 'invoices':
                    $cleared_items[] = __('Invoices', 'simple-invoice');
                    break;
                case 'clients':
                    $cleared_items[] = __('Clients', 'simple-invoice');
                    break;
                case 'templates':
                    $cleared_items[] = __('Design Templates', 'simple-invoice');
                    break;
                case 'custom_templates':
                    $cleared_items[] = __('Custom Templates', 'simple-invoice');
                    break;
            }
        }

        $message = sprintf(
            __('Successfully cleared: %s', 'simple-invoice'),
            implode(', ', $cleared_items)
        );

        wp_send_json_success($message);
    }

    /**
     * AJAX handler for resetting settings to default
     *
     * @since 1.0.0
     */
    public function si_ajax_reset_settings() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'si_reset_settings_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        // Get default settings
        $default_settings = array(
            'business_name' => get_bloginfo('name'),
            'business_address' => '',
            'business_email' => get_option('admin_email'),
            'business_phone' => '',
            'business_logo' => '',
            'gstin' => '',
            'default_due_days' => 7,
            'currency' => 'USD',
            'currency_symbol' => '$',
            'payment_methods' => array('bank', 'upi'),
            'bank_details' => '',
            'paypal_email' => '',
            'upi_id' => '',
            'footer_notes' => 'Thank you for your business!',
            'terms_text' => 'Payment is due within the specified due date.',
            'clear_data_on_deactivation' => 0
        );

        // Update settings with defaults
        $result = update_option('si_settings', $default_settings);

        if ($result) {
            wp_send_json_success(__('Settings have been reset to default values.', 'simple-invoice'));
        } else {
            wp_send_json_error(__('Failed to reset settings.', 'simple-invoice'));
        }
    }

    /**
     * Clear only custom templates (not default template)
     *
     * @return bool True on success, false on failure
     * @since 1.0.0
     */
    private function si_clear_custom_templates() {
        global $wpdb;

        try {
            // Delete all templates except the default one
            $result = $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$wpdb->prefix}wimp_templates WHERE name != %s",
                    'Default Template'
                )
            );

            return $result !== false;
        } catch (Exception $e) {

            return false;
        }
    }

    /**
     * AJAX handler to get template data
     *
     * @since 1.0.0
     */
    public function si_ajax_get_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'si_get_template_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('You do not have permission to perform this action.', 'wp-invoice-manager-pro'));
        }

        $template_id = intval($_POST['template_id']);
        if (!$template_id) {
            wp_send_json_error(__('Invalid template ID.', 'simple-invoice'));
        }

        // Get template data
        $template_manager = new SI_Template();
        $template = $template_manager->si_get_template($template_id);

        if (!$template) {
            wp_send_json_error(__('Template not found.', 'simple-invoice'));
        }

        wp_send_json_success($template);
    }

    /**
     * AJAX handler to add new template
     *
     * @since 1.0.0
     */
    public function si_ajax_add_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'si_add_template_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('You do not have permission to perform this action.', 'wp-invoice-manager-pro'));
        }

        // Validate required fields
        if (empty($_POST['name'])) {
            wp_send_json_error(__('Template name is required.', 'simple-invoice'));
        }

        // Prepare template data with proper sanitization
        $template_data = array(
            'name' => sanitize_text_field($_POST['name']),
            'design' => sanitize_text_field($_POST['design'] ?? 'classic'),
            'description' => sanitize_textarea_field($_POST['description'] ?? ''),
            'header_fields' => isset($_POST['header_fields']) ? $_POST['header_fields'] : array(),
            'table_fields' => isset($_POST['table_fields']) ? $_POST['table_fields'] : array(),
            'summary_fields' => isset($_POST['summary_fields']) ? $_POST['summary_fields'] : array()
        );

        // Save template
        $template_manager = new SI_Template();
        $result = $template_manager->si_create_template($template_data);

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Template saved successfully.', 'simple-invoice'),
                'template_id' => $result
            ));
        } else {
            wp_send_json_error(__('Failed to save template. Template name may already exist.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler to edit template
     *
     * @since 1.0.0
     */
    public function si_ajax_edit_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'si_edit_template_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('You do not have permission to perform this action.', 'wp-invoice-manager-pro'));
        }

        $template_id = intval($_POST['template_id']);
        if (!$template_id) {
            wp_send_json_error(__('Invalid template ID.', 'simple-invoice'));
        }

        // Validate required fields
        if (empty($_POST['name'])) {
            wp_send_json_error(__('Template name is required.', 'simple-invoice'));
        }

        // Prepare template data
        $template_data = array(
            'id' => $template_id,
            'name' => sanitize_text_field($_POST['name']),
            'design' => sanitize_text_field($_POST['design']),
            'header_fields' => isset($_POST['header_fields']) ? $_POST['header_fields'] : array(),
            'body_fields' => isset($_POST['body_fields']) ? $_POST['body_fields'] : array(),
            'summary_fields' => isset($_POST['summary_fields']) ? $_POST['summary_fields'] : array()
        );

        // Update template
        $template_manager = new SI_Template();
        $result = $template_manager->si_update_template($template_id, $template_data);

        if ($result) {
            wp_send_json_success(__('Template updated successfully.', 'simple-invoice'));
        } else {
            wp_send_json_error(__('Failed to update template. Please try again.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler to delete template
     *
     * @since 1.0.0
     */
    public function si_ajax_delete_template() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'si_delete_template_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('You do not have permission to perform this action.', 'wp-invoice-manager-pro'));
        }

        $template_id = intval($_POST['template_id']);
        if (!$template_id) {
            wp_send_json_error(__('Invalid template ID.', 'simple-invoice'));
        }

        // Delete template
        $template_manager = new SI_Template();
        $result = $template_manager->si_delete_template($template_id);

        if ($result) {
            wp_send_json_success(__('Template deleted successfully.', 'simple-invoice'));
        } else {
            wp_send_json_error(__('Failed to delete template. Please try again.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for design preview
     *
     * @since 1.0.0
     */
    public function si_ajax_preview_design() {
        // Verify nonce
        if (!wp_verify_nonce($_GET['_wpnonce'] ?? '', 'si_preview_design')) {
            wp_die(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_die(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        $design_id = sanitize_text_field($_GET['design_id'] ?? '');

        if (empty($design_id)) {
            wp_die(__('Invalid design ID.', 'simple-invoice'));
        }

        // Load design
        $design_loader = new SI_Design_Loader();
        $available_designs = $design_loader->si_get_available_designs();

        if (!isset($available_designs[$design_id])) {
            wp_die(__('Design not found.', 'simple-invoice'));
        }

        // Create sample invoice data for preview
        $sample_data = $this->si_get_sample_invoice_data();

        // Load the design template
        $design_path = WIMP_PLUGIN_PATH . 'designs/' . $design_id . '/template.php';

        if (!file_exists($design_path)) {
            wp_die(__('Design template file not found.', 'simple-invoice'));
        }

        // Extract sample data variables
        extract($sample_data);

        // Define helper function for currency formatting in preview
        if (!function_exists('si_format_currency')) {
            function si_format_currency($amount) {
                // Get currency symbol from settings
                $settings = si_get_settings();
                $currency_symbol = $settings['currency_symbol'] ?? '$';
                return $currency_symbol . number_format((float)$amount, 2);
            }
        }

        // Start output buffering
        ob_start();

        // Include the design template
        include $design_path;

        // Get the output
        $output = ob_get_clean();

        // Add some CSS for better preview
        $preview_css = '
        <style>
            body {
                margin: 20px;
                font-family: Arial, sans-serif;
                background: #f5f5f5;
            }
            .invoice-container {
                background: white;
                max-width: 800px;
                margin: 0 auto;
                padding: 40px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                border-radius: 8px;
            }
            @media print {
                body { margin: 0; background: white; }
                .invoice-container { box-shadow: none; margin: 0; border-radius: 0; }
            }
        </style>';

        // Output the preview
        echo '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Design Preview: ' . esc_html(ucfirst($design_id)) . '</title>
            ' . $preview_css . '
        </head>
        <body>';

        echo $output;

        echo '</body></html>';

        exit;
    }

    /**
     * Get sample invoice data for preview
     *
     * @return array Sample invoice data
     * @since 1.0.0
     */
    private function si_get_sample_invoice_data() {
        return array(
            'settings' => array(
                'business_name' => 'Sample Business Inc.',
                'business_address' => "123 Business Street\nSuite 100\nCity, State 12345",
                'business_email' => '<EMAIL>',
                'business_phone' => '(*************',
                'gstin' => 'GST123456789',
                'business_logo' => ''
            ),
            'client' => (object) array(
                'name' => 'John Doe',
                'business_name' => 'Client Company Ltd.',
                'address' => "456 Client Avenue\nClient City, State 67890",
                'email' => '<EMAIL>',
                'contact_number' => '(*************',
                'gstin' => 'GST987654321'
            ),
            'invoice_number' => 'INV-2025-001',
            'invoice_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'items' => array(
                array(
                    'description' => 'Web Development Services',
                    'quantity' => 1,
                    'rate' => 5000.00
                ),
                array(
                    'description' => 'UI/UX Design',
                    'quantity' => 2,
                    'rate' => 2500.00
                ),
                array(
                    'description' => 'Consultation Hours',
                    'quantity' => 5,
                    'rate' => 1000.00
                )
            ),
            'subtotal' => 15000.00,
            'tax_rate' => 18,
            'tax_amount' => 2700.00,
            'discount_amount' => 0,
            'shipping_amount' => 0,
            'total_amount' => 17700.00,
            'notes' => 'Thank you for your business! Payment is due within 30 days of invoice date. Please include invoice number with your payment.'
        );
    }

    /**
     * AJAX handler to get template fields
     *
     * @since 1.0.0
     */
    public function si_ajax_get_template_fields() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'si_get_template_fields_nonce')) {
            wp_send_json_error(__('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('You do not have permission to perform this action.', 'wp-invoice-manager-pro'));
        }

        $template_id = intval($_POST['template_id']);
        if (!$template_id) {
            wp_send_json_error(__('Invalid template ID.', 'simple-invoice'));
        }

        // Get template data
        $template_manager = new SI_Template();
        $template = $template_manager->si_get_template($template_id);

        if (!$template) {
            wp_send_json_error(__('Template not found.', 'simple-invoice'));
        }

        // Parse body fields (table fields) - only return what's actually configured
        $body_fields = array();
        if (!empty($template->body_fields)) {
            $body_fields = is_string($template->body_fields) ?
                json_decode($template->body_fields, true) :
                $template->body_fields;
        }

        // Parse summary fields - only return what's actually configured
        $summary_fields = array();
        if (!empty($template->summary_fields)) {
            $summary_fields = is_string($template->summary_fields) ?
                json_decode($template->summary_fields, true) :
                $template->summary_fields;
        }

        // Only return the fields that are actually configured - no defaults
        wp_send_json_success(array(
            'body_fields' => $body_fields,
            'summary_fields' => $summary_fields,
            'template_name' => $template->name,
            'design' => $template->design
        ));
    }

    /**
     * AJAX handler to get client data
     *
     * @since 1.0.0
     */
    public function si_ajax_get_client() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'si_get_client_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-invoice-manager-pro'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        $client_id = intval($_POST['client_id'] ?? 0);
        if (!$client_id) {
            wp_send_json_error(__('Invalid client ID.', 'wp-invoice-manager-pro'));
        }

        $client = si_get_client($client_id);
        if ($client) {
            wp_send_json_success($client);
        } else {
            wp_send_json_error(__('Client not found.', 'wp-invoice-manager-pro'));
        }
    }

    /**
     * AJAX handler to add new client
     *
     * @since 1.0.0
     */
    public function si_ajax_add_client() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'si_add_client_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-invoice-manager-pro'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        // Sanitize input data
        $client_data = array(
            'name' => si_sanitize_text($_POST['name'] ?? ''),
            'business_name' => si_sanitize_text($_POST['business_name'] ?? ''),
            'address' => si_sanitize_textarea($_POST['address'] ?? ''),
            'contact_number' => si_sanitize_text($_POST['phone'] ?? $_POST['contact_number'] ?? ''),
            'email' => si_sanitize_email($_POST['email'] ?? ''),
            'gstin' => si_sanitize_text($_POST['gst_number'] ?? $_POST['gstin'] ?? '')
        );

        // Validate required fields
        if (empty($client_data['name'])) {
            wp_send_json_error(__('Client name is required.', 'wp-invoice-manager-pro'));
        }

        // Create client using SI_Client class
        $client_manager = new SI_Client();
        $client_id = $client_manager->si_create_client($client_data);

        if ($client_id) {
            wp_send_json_success(array(
                'message' => __('Client added successfully.', 'wp-invoice-manager-pro'),
                'client_id' => $client_id
            ));
        } else {
            wp_send_json_error(__('Failed to add client.', 'wp-invoice-manager-pro'));
        }
    }

    /**
     * AJAX handler to edit client
     *
     * @since 1.0.0
     */
    public function si_ajax_edit_client() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'si_edit_client_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-invoice-manager-pro'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        $client_id = intval($_POST['client_id'] ?? 0);
        if (!$client_id) {
            wp_send_json_error(__('Invalid client ID.', 'wp-invoice-manager-pro'));
        }

        // Sanitize input data
        $client_data = array(
            'name' => si_sanitize_text($_POST['name'] ?? ''),
            'business_name' => si_sanitize_text($_POST['business_name'] ?? ''),
            'address' => si_sanitize_textarea($_POST['address'] ?? ''),
            'contact_number' => si_sanitize_text($_POST['phone'] ?? $_POST['contact_number'] ?? ''),
            'email' => si_sanitize_email($_POST['email'] ?? ''),
            'gstin' => si_sanitize_text($_POST['gst_number'] ?? $_POST['gstin'] ?? '')
        );

        // Validate required fields
        if (empty($client_data['name'])) {
            wp_send_json_error(__('Client name is required.', 'wp-invoice-manager-pro'));
        }

        // Update client using SI_Client class
        $client_manager = new SI_Client();
        $success = $client_manager->si_update_client($client_id, $client_data);

        if ($success) {
            wp_send_json_success(__('Client updated successfully.', 'wp-invoice-manager-pro'));
        } else {
            wp_send_json_error(__('Failed to update client.', 'wp-invoice-manager-pro'));
        }
    }

    /**
     * AJAX handler to delete client
     *
     * @since 1.0.0
     */
    public function si_ajax_delete_client() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'si_delete_client_nonce')) {
            wp_send_json_error(__('Security check failed.', 'wp-invoice-manager-pro'));
        }

        // Check user capabilities
        if (!current_user_can('read')) {
            wp_send_json_error(__('Insufficient permissions.', 'wp-invoice-manager-pro'));
        }

        $client_id = intval($_POST['client_id'] ?? 0);
        if (!$client_id) {
            wp_send_json_error(__('Invalid client ID.', 'wp-invoice-manager-pro'));
        }

        // Delete client using SI_Client class
        $client_manager = new SI_Client();
        $success = $client_manager->si_delete_client($client_id);

        if ($success) {
            wp_send_json_success(__('Client deleted successfully.', 'wp-invoice-manager-pro'));
        } else {
            wp_send_json_error(__('Failed to delete client.', 'wp-invoice-manager-pro'));
        }
    }
}
